//
//  DeviceActivityReportExtension.swift
//  ActivityReportExtension
//
//  Entry point for the Screen-Time *Device-Activity Report* extension.
//  The `@main` annotation generates the required __swift5_entry section
//  that iOS 18 expects for every Swift-based ExtensionKit bundle.
//

import DeviceActivity
import SwiftUI

@main
struct PravaReportExtension: DeviceActivityReportExtension {

    // Provide *one* DeviceActivityReportScene per supported context.
    // The builder attached to `body` lets you list them one-per-line.
    var body: some DeviceActivityReportScene {
        // Create instances directly
        TotalActivityReport()
        GlobalUsageReport()
    }
}
