//
//  TotalActivityReport.swift
//  com.redpill.client.ios.report
//

import SwiftUI
import DeviceActivity
import os.log
import FamilyControls

// ───────────────────────────── Device-Activity Report Scene ─────────────────────────────
@preconcurrency
struct TotalActivityReport: DeviceActivityReportScene {
    let context = DeviceActivityReport.Context("TotalActivity")
    
    // The view is rendered in the main app, but we don't need to show anything.
    // Make it nonisolated and use @Sendable to ensure it's safe for concurrency
    nonisolated let content: @Sendable (String) -> EmptyView = { _ in EmptyView() }
    
    nonisolated func makeConfiguration(
        representing data: DeviceActivityResults<DeviceActivityData>
    ) async -> String {
        let log = Logger(subsystem: "com.redpill.client.ios.report", category: "TotalActivityReport")
        log.info("TotalActivityReport.makeConfiguration called")
        
        let store = await ActivityStore.shared
        var recordsProcessed = 0
        var allRecords: [ActivityRecord] = []
        
        // Create an iterator for the AsyncSequence
        let iterator = data.makeAsyncIterator()
        
        // Process each element in the sequence
        while let activityData = await iterator.next() {
            log.info("Processing activity data")

            // Process activity segments (new API structure)
            for await activitySegment in activityData.activitySegments {
                log.info("Processing activity segment with duration: \(activitySegment.totalActivityDuration)")

                // Process categories within the segment to extract app usage
                for await categoryActivity in activitySegment.categories {
                    // Process applications within the category
                    for await appActivity in categoryActivity.applications {
                        // Per business goal, use the bundle ID as the source
                        let bundleIdentifier = appActivity.application.bundleIdentifier ?? "Unknown App"
                        let duration = appActivity.totalActivityDuration

                        if duration > 0 {
                            let now = Date()
                            let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                            let endMs = Int64(now.timeIntervalSince1970 * 1000)

                            let record = ActivityRecord(
                                source: bundleIdentifier,
                                subtype: "app.usage",
                                begin: beginMs,
                                end: endMs,
                                userTimezone: TimeZone.current.secondsFromGMT(),
                                workingIntensity: 1.0
                            )

                            allRecords.append(record)
                            recordsProcessed += 1
                            log.info("Created record for app \(bundleIdentifier)")
                        }
                    }
                }
            }
        }
        
        // Save all records
        if !allRecords.isEmpty {
            await store.append(contentsOf: allRecords)
        }
        
        log.info("Processing complete. \(recordsProcessed) records were saved.")
        
        // Encode to base64 for return
        do {
            let jsonData = try JSONEncoder().encode(allRecords)
            return jsonData.base64EncodedString()
        } catch {
            log.error("Failed to encode records: \(error.localizedDescription)")
            return ""
        }
    }
}
