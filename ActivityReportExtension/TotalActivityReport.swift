//
//  TotalActivityReport.swift
//  com.redpill.client.ios.report
//

import SwiftUI
import DeviceActivity
import os.log
import FamilyControls

// ───────────────────────────── Device-Activity Report Scene ─────────────────────────────
struct TotalActivityReportConfiguration {
    let totalDuration: TimeInterval
    let appUsageData: [AppUsageData]
}

struct AppUsageData: Identifiable {
    let id = UUID()
    let appName: String
    let bundleIdentifier: String
    let duration: TimeInterval
}

@preconcurrency
struct TotalActivityReport: DeviceActivityReportScene {
    let context = DeviceActivityReport.Context("TotalActivity")

    // Return a proper view that shows the activity data
    nonisolated let content: @Sendable (TotalActivityReportConfiguration) -> TotalActivityReportView = { config in
        TotalActivityReportView(configuration: config)
    }
    
    nonisolated func makeConfiguration(
        representing data: DeviceActivityResults<DeviceActivityData>
    ) async -> TotalActivityReportConfiguration {
        let log = Logger(subsystem: "com.redpill.client.ios.report", category: "TotalActivityReport")
        log.info("TotalActivityReport.makeConfiguration called")
        
        let store = await CoreDataActivityStore.shared
        var recordsProcessed = 0
        var appUsageData: [AppUsageData] = []
        var totalDuration: TimeInterval = 0

        // Process in smaller batches to reduce memory usage
        var batchRecords: [ActivityRecord] = []
        let batchSize = 50 // Process in smaller batches

        // Create an iterator for the AsyncSequence
        var iterator = data.makeAsyncIterator()
        
        // Process each element in the sequence
        while let activityData = await iterator.next() {
            log.info("Processing activity data")

            // Process activity segments (new API structure)
            for await activitySegment in activityData.activitySegments {
                log.info("Processing activity segment with duration: \(activitySegment.totalActivityDuration)")

                // Process categories within the segment to extract app usage
                for await categoryActivity in activitySegment.categories {
                    // Process applications within the category
                    for await appActivity in categoryActivity.applications {
                        // Per business goal, use the bundle ID as the source
                        let bundleIdentifier = appActivity.application.bundleIdentifier ?? "Unknown App"
                        let appName = appActivity.application.localizedDisplayName ?? bundleIdentifier
                        let duration = appActivity.totalActivityDuration

                        if duration > 0 {
                            totalDuration += duration

                            // Create app usage data for the report view
                            let appUsage = AppUsageData(
                                appName: appName,
                                bundleIdentifier: bundleIdentifier,
                                duration: duration
                            )
                            appUsageData.append(appUsage)

                            let now = Date()
                            let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                            let endMs = Int64(now.timeIntervalSince1970 * 1000)

                            let record = ActivityRecord(
                                source: bundleIdentifier,
                                subtype: "app.usage",
                                begin: beginMs,
                                end: endMs,
                                userTimezone: TimeZone.current.secondsFromGMT() / 60, // Convert to minutes
                                workingIntensity: 1.0
                            )

                            batchRecords.append(record)
                            recordsProcessed += 1

                            // Process in batches to reduce memory usage
                            if batchRecords.count >= batchSize {
                                await store.append(contentsOf: batchRecords)
                                batchRecords.removeAll()
                            }

                            log.info("Created record for app \(bundleIdentifier) with duration \(duration)s")
                        }
                    }
                }
            }
        }

        // Save any remaining records in the final batch
        if !batchRecords.isEmpty {
            await store.append(contentsOf: batchRecords)
        }
        
        log.info("Processing complete. \(recordsProcessed) records were saved. Total duration: \(totalDuration)s")

        // Return the configuration for the report view
        return TotalActivityReportConfiguration(
            totalDuration: totalDuration,
            appUsageData: appUsageData.sorted { $0.duration > $1.duration }
        )
    }
}

struct TotalActivityReportView: View {
    let configuration: TotalActivityReportConfiguration

    private var totalDurationFormatted: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: configuration.totalDuration) ?? "0m"
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
            // Total usage summary
            VStack(alignment: .leading, spacing: 8) {
                Text("Total Screen Time")
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(totalDurationFormatted)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)

            // App usage list
            if !configuration.appUsageData.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("App Usage")
                        .font(.headline)
                        .foregroundColor(.primary)

                    ForEach(configuration.appUsageData.prefix(10)) { appUsage in
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text(appUsage.appName)
                                    .font(.body)
                                    .fontWeight(.medium)

                                Text(appUsage.bundleIdentifier)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Text(formatDuration(appUsage.duration))
                                .font(.body)
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                        }
                        .padding(.vertical, 4)

                        if appUsage.id != configuration.appUsageData.prefix(10).last?.id {
                            Divider()
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            } else {
                Text("No app usage data available")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            }

            }
            .padding()
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? "0m"
    }
}
