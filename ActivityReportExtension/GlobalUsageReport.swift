//
//  GlobalUsageReport.swift
//  com.redpill.client.ios.report
//

import SwiftUI
import FamilyControls
import DeviceActivity
import os.log

@preconcurrency
struct GlobalUsageReport: DeviceActivityReportScene {
    
    let context = DeviceActivityReport.Context("GlobalUsage")
    
    // The view is rendered in the main app, but we don't need to show anything.
    // Make it nonisolated and use @Sendable to ensure it's safe for concurrency
    nonisolated let content: @Sendable (String) -> EmptyView = { _ in EmptyView() }

    nonisolated func makeConfiguration(
        representing data: DeviceActivityResults<DeviceActivityData>
    ) async -> String {
        let log = Logger(
            subsystem: "com.redpill.client.ios.report",
            category: "GlobalUsageReport"
        )
        log.info("GlobalUsageReport.makeConfiguration called")
        
        let store = await ActivityStore.shared
        var recordsProcessed = 0
        var allRecords: [ActivityRecord] = []

        // Create an iterator for the AsyncSequence
        var iterator = data.makeAsyncIterator()
        
        // Process each element in the sequence
        while let activityData = await iterator.next() {
            log.info("Processing activity data")
            
            // Process category activity
            for categoryActivity in activityData.categoryActivity {
                let categoryToken = categoryActivity.category.token
                let categoryIdentifier = categoryToken.localizedDisplayName ?? "Unknown Category"
                let duration = categoryActivity.totalActivityDuration
                
                if duration > 0 {
                    let now = Date()
                    let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                    let endMs = Int64(now.timeIntervalSince1970 * 1000)
                    
                    let record = ActivityRecord(
                        source: "category:\(categoryIdentifier)",
                        subtype: "category.usage",
                        begin: beginMs,
                        end: endMs,
                        userTimezone: TimeZone.current.secondsFromGMT(),
                        workingIntensity: 1.0
                    )
                    
                    allRecords.append(record)
                    recordsProcessed += 1
                    log.info("Created record for category \(categoryIdentifier)")
                }
            }
            
            // Process application activity
            for appActivity in activityData.applicationActivity {
                let bundleIdentifier = appActivity.application.bundleIdentifier ?? "Unknown App"
                let duration = appActivity.totalActivityDuration
                
                if duration > 0 {
                    let now = Date()
                    let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                    let endMs = Int64(now.timeIntervalSince1970 * 1000)
                    
                    let record = ActivityRecord(
                        source: bundleIdentifier,
                        subtype: "app.usage",
                        begin: beginMs,
                        end: endMs,
                        userTimezone: TimeZone.current.secondsFromGMT(),
                        workingIntensity: 1.0
                    )
                    
                    allRecords.append(record)
                    recordsProcessed += 1
                    log.info("Created record for app \(bundleIdentifier)")
                }
            }
        }
        
        // Save all records
        if !allRecords.isEmpty {
            await store.append(contentsOf: allRecords)
        }
        
        log.info("Processing complete. \(recordsProcessed) records were saved.")
        
        // Encode to base64 for return
        do {
            let jsonData = try JSONEncoder().encode(allRecords)
            return jsonData.base64EncodedString()
        } catch {
            log.error("Failed to encode records: \(error.localizedDescription)")
            return ""
        }
    }
}
