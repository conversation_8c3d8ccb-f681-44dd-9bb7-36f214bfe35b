//
//  ActivityMonitorExtension.swift
//  redpill.client.ios.monitor
//
//  This extension now correctly handles only the start and stop of monitoring.
//

// --- FIX: Add `@preconcurrency` to acknowledge non-Sendable types from this module ---
@preconcurrency import DeviceActivity
import Foundation
import os.log

class ActivityMonitorExtension: DeviceActivityMonitor {
    private let log = Logger(subsystem: "com.redpill.client.ios.monitor", category: "ActivityMonitor")

    override func intervalDidStart(for activity: DeviceActivityName) {
        super.intervalDidStart(for: activity)
        log.info("Monitoring interval started for \(activity.rawValue).")
    }

    override func intervalDidEnd(for activity: DeviceActivityName) {
        super.intervalDidEnd(for: activity)
        log.info("Monitoring interval ended for \(activity.rawValue).")
    }
    
    // --- REMOVED: The `eventDidReachThreshold` and static heartbeat property ---
    // This removes the source of the concurrency warnings and simplifies the extension,
    // as all data processing is now correctly handled by the report extension.
}
