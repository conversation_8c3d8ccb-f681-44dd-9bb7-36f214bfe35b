//
//  DeviceActivityMonitorExtension.swift
//  ActivityMonitorExtension
//
//  Created by <PERSON> on 26/05/2025.
//

import DeviceActivity

class DeviceActivityMonitorExtension: DeviceActivityMonitor {
    
    override func intervalDidStart(for activity: DeviceActivityName) {
        super.intervalDidStart(for: activity)
        AppLogger.screenTime.info("Monitoring interval started: \(activity.rawValue)")
    }
    
    override func intervalDidEnd(for activity: DeviceActivityName) {
        super.intervalDidEnd(for: activity)
        AppLogger.screenTime.info("Monitoring interval ended: \(activity.rawValue)")
    }
    
    override func eventDidReachThreshold(_ event: DeviceActivityEvent.Name, activity: DeviceActivityName) {
        super.eventDidReachThreshold(event, activity: activity)
        // FIX: We no longer record data here. This log just confirms the system is working.
        // The actual data collection happens in the Report Extension.
        AppLogger.screenTime.info("Threshold reached for event '\(event.rawValue)'. A report can now be generated.")
    }
}
