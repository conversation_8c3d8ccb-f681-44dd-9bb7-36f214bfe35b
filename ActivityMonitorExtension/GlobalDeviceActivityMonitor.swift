//
//  GlobalDeviceActivityMonitor.swift
//  ActivityMonitorExtension   (target: com.redpill.client.ios.monitor)
//
//  IMPORTANT: this file must **only** belong to the monitor-extension target.
//  Do **not** add it to the main-app or report targets.
//

import DeviceActivity
import os.log

/// Entry point for the Device-Activity Monitor extension.
final class GlobalDeviceActivityMonitor: DeviceActivityMonitor {

    private let log = Logger(subsystem: "com.redpill.client.ios.monitor",
                             category: "Monitor")

    // MARK: – Lifecycle callbacks ------------------------------------------------
    /// Called by iOS at midnight because the host app schedules a 24 h interval.
    override func intervalDidStart(for activity: DeviceActivityName) {
        log.info("Interval did start for \(activity.rawValue, privacy: .public)")
    }

    override func intervalDidEnd(for activity: DeviceActivityName) {
        log.info("Interval did end for \(activity.rawValue, privacy: .public)")
    }

    /// We only collect usage, so thresholds are ignored.
    override func eventDidReachThreshold(_ event: DeviceActivityEvent.Name,
                                         activity: DeviceActivityName) { }
}
