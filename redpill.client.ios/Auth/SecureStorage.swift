//
//  SecureStorage.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//


import Foundation

struct SecureStorage {
    static var userId: String? {
        get { UserDefaults.standard.string(forKey: "userId") }
        set { UserDefaults.standard.set(newValue, forKey: "userId") }
    }
    static var userEmail: String? {
        get { UserDefaults.standard.string(forKey: "userEmail") }
        set { UserDefaults.standard.set(newValue, forKey: "userEmail") }
    }
    static var idToken: String? {
        get { UserDefaults.standard.string(forKey: "idToken") }
        set { UserDefaults.standard.set(newValue, forKey: "idToken") }
    }
}