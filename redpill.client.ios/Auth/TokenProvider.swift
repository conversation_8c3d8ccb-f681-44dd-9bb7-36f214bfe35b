//
//  TokenProvider.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//


import FirebaseAuth

actor To<PERSON><PERSON><PERSON><PERSON> {
    static let shared = TokenProvider()
    private var idToken: String?
    private let expiryBuffer: TimeInterval = 300
    
    func cache(idToken: String?) {
        self.idToken = idToken
    }
    
    func validToken() async throws -> String {
        if let token = idToken, !isExpired(token, buffer: expiryBuffer) {
            return token
        }
        guard let user = Auth.auth().currentUser else {
            throw NSError(domain: "Auth", code: 1, userInfo: [NSLocalizedDescriptionKey: "No user signed in"])
        }
        let fresh = try await user.getIDToken(forcingRefresh: true)
        idToken = fresh
        SecureStorage.idToken = fresh
        return fresh
    }
    
    private func isExpired(_ jwt: String, buffer: TimeInterval) -> Bool {
        let parts = jwt.split(separator: ".")
        guard parts.count == 3,
              let data = Data(base64Encoded: String(parts[1]).base64URLToBase64()),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let exp = json["exp"] as? TimeInterval else {
            return true
        }
        return Date(timeIntervalSince1970: exp - buffer) <= Date()
    }
}

private extension String {
    func base64URLToBase64() -> String {
        padding(toLength: ((count + 3) / 4) * 4, withPad: "=", startingAt: 0)
            .replacingOccurrences(of: "-", with: "+")
            .replacingOccurrences(of: "_", with: "/")
    }
}