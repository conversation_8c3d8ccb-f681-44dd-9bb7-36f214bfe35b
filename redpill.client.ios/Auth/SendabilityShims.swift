//
//  SendabilityShims.swift
//  redpill.client.ios  ◇  shared with ActivityReportExtension & MonitorExtension
//
//  Purpose: silence “non-Sendable result type” diagnostics for Firebase / GID
//  until the vendors ship their own `Sendable` annotations.
//

@preconcurrency import FirebaseAuth        // ✅ attribute belongs on *import*
@preconcurrency import FirebaseCore

#if canImport(GoogleSignIn)
@preconcurrency import GoogleSignIn        // ✅ same here
#endif

// MARK: – Firebase Auth
extension FirebaseAuth.AuthDataResult : @retroactive @unchecked Sendable {}
extension FirebaseAuth.User          : @retroactive @unchecked Sendable {}

// MARK: – Google Sign-In
#if canImport(GoogleSignIn)
extension GoogleSignIn.GIDSignInResult : @retroactive @unchecked Sendable {}
#endif
