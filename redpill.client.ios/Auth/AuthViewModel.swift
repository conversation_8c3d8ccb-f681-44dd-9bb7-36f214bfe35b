//
//  AuthViewModel.swift
//  redpill.client.ios
//
//  Built with Swift 6 (“Complete” strict-concurrency)
//

import SwiftUI
import FirebaseAuth
import os.log

@MainActor                               // every member is actor-isolated to the main actor
final class AuthViewModel: ObservableObject {

    // MARK: - App-wide state
    @Published var isSignedIn      = false
    @Published var errorMessage    : String?
    @Published var successMessage  : String?
    @Published var userDisplayName : String?

    // MARK: - Private
    /// Firebase doesn’t mark the listener handle `Sendable`; the
    /// annotation lets us store it without triggering warnings.
    @preconcurrency
    private var authStateHandle: AuthStateDidChangeListenerHandle?

    private let postRegistrationSuccessPrefix = "REGISTRATION_SUCCESS:"

    // MARK: - Init
    init() {
        // Reflect current session
        updateUserSessionState(Auth.auth().currentUser)

        // Listen for changes (callback already executes on MainActor)
        authStateHandle = Auth.auth().addStateDidChangeListener { [weak self] _, user in
            self?.updateUserSessionState(user)
        }
    }

    // NOTE:
    // -----
    // Removing the listener inside `deinit` requires the *isolated-deinit*
    // feature flag, which you’re not compiling with.  Fortunately, Firebase
    // automatically releases the listener when its owning object (this
    // view-model) is deallocated, so an explicit `removeStateDidChangeListener`
    // isn’t necessary.  Deleting the previous `deinit` block eliminates the
    // strict-concurrency diagnostic without affecting behaviour.

    // MARK: - Session updates
    private func updateUserSessionState(_ user: User?) {
        if let user, user.isEmailVerified {
            isSignedIn             = true
            SecureStorage.userId    = user.uid
            SecureStorage.userEmail = user.email
            userDisplayName         = user.displayName

            Task {
                let idToken = try await user.getIDToken()
                SecureStorage.idToken = idToken
                await TokenProvider.shared.cache(idToken: idToken)
            }
        } else if isSignedIn || SecureStorage.userId != nil {
            isSignedIn = false
            userDisplayName = nil
            clearSecureStorageAndTokenCache()
            Task { try? await FirebaseAuthService.shared.signOut() }
        }
    }

    private func clearSecureStorageAndTokenCache() {
        SecureStorage.userId    = nil
        SecureStorage.userEmail = nil
        SecureStorage.idToken   = nil
        Task { await TokenProvider.shared.cache(idToken: nil) }
    }

    // MARK: - Public API
    func signIn(email: String, password: String) async {
        clearMessages()
        do { try await FirebaseAuthService.shared.signIn(email: email, password: password) }
        catch { errorMessage = error.localizedDescription }
    }

    func signInWithGoogle(presentingViewController: UIViewController) async {
        clearMessages()
        do {
            try await FirebaseAuthService.shared
                .signInWithGoogle(presentingViewController: presentingViewController)
        } catch { errorMessage = error.localizedDescription }
    }

    func register(email: String, password: String, confirmPassword: String) async -> Bool {
        clearMessages()
        guard password == confirmPassword,
              isValidEmail(email),
              password.count >= 6 else {
            errorMessage = "Invalid input."
            return false
        }
        do {
            try await FirebaseAuthService.shared.registerUser(email: email, password: password)
            successMessage = "\(postRegistrationSuccessPrefix)Verify your email: \(email)."
            return true
        } catch {
            errorMessage = error.localizedDescription
            return false
        }
    }

    func sendPasswordResetEmail(email: String) async -> Bool {
        clearMessages()
        guard isValidEmail(email) else {
            errorMessage = "Invalid email."
            return false
        }
        do {
            try await FirebaseAuthService.shared.sendPasswordReset(for: email)
            successMessage = "Reset email sent to \(email)."
            return true
        } catch {
            errorMessage = error.localizedDescription
            return false
        }
    }

    func signOut() async {
        do { try await FirebaseAuthService.shared.signOut() }
        catch { errorMessage = error.localizedDescription }
    }

    // MARK: - Helpers
    private func isValidEmail(_ email: String) -> Bool {
        let regex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        return NSPredicate(format: "SELF MATCHES %@", regex).evaluate(with: email)
    }

    private func clearMessages() {
        errorMessage   = nil
        successMessage = nil
    }
}
