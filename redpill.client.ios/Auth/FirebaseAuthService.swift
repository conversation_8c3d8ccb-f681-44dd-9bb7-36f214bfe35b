//
//  FirebaseAuthService.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//


import Foundation
import FirebaseAuth
import GoogleSignIn
import UIKit
import os.log
import FirebaseCore

@MainActor
final class FirebaseAuthService {
    static let shared = FirebaseAuthService()
    private init() {}
    
    private let PravaAuthErrorDomain = "com.prava.auth"
    enum PravaAuthErrorCode: Int {
        case emailNotVerified = 10001
        case generalAuthError = 10002
        case missingClientID = 10003
        case googleMissingIDToken = 10004
    }
    
    private func createError(code: PravaAuthErrorCode, description: String, underlyingError: Error? = nil) -> NSError {
        var userInfo: [String: Any] = [NSLocalizedDescriptionKey: description]
        if let underlyingError = underlyingError {
            userInfo[NSUnderlyingErrorKey] = underlyingError
        }
        return NSError(domain: PravaAuthErrorDomain, code: code.rawValue, userInfo: userInfo)
    }
    
    func signIn(email: String, password: String) async throws {
        do {
            let result = try await Auth.auth().signIn(withEmail: email, password: password)
            guard result.user.isEmailVerified else {
                try? Auth.auth().signOut()
                throw createError(code: .emailNotVerified, description: "Please verify your email.")
            }
        } catch let error as NSError {
            throw createError(code: .generalAuthError, description: error.localizedDescription, underlyingError: error)
        }
    }
    
    func signInWithGoogle(presentingViewController: UIViewController) async throws {
        guard let clientID = FirebaseApp.app()?.options.clientID else {
            throw createError(code: .missingClientID, description: "Missing Firebase client ID.")
        }
        // Set the configuration globally if needed (optional in most cases with Firebase)
        GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientID)
        
        do {
            // Updated method for version 8.0
            let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController)
            guard let idToken = result.user.idToken?.tokenString else {
                throw createError(code: .googleMissingIDToken, description: "Google ID token missing.")
            }
            let credential = GoogleAuthProvider.credential(withIDToken: idToken, accessToken: result.user.accessToken.tokenString)
            let fbResult = try await Auth.auth().signIn(with: credential)
            guard fbResult.user.isEmailVerified else {
                try? Auth.auth().signOut()
                GIDSignIn.sharedInstance.signOut()
                throw createError(code: .emailNotVerified, description: "Email not verified.")
            }
        } catch {
            GIDSignIn.sharedInstance.signOut()
            throw createError(code: .generalAuthError, description: error.localizedDescription, underlyingError: error)
        }
    }
    
    func registerUser(email: String, password: String) async throws {
        do {
            let result = try await Auth.auth().createUser(withEmail: email, password: password)
            try await result.user.sendEmailVerification()
            try Auth.auth().signOut()
        } catch {
            throw createError(code: .generalAuthError, description: error.localizedDescription, underlyingError: error)
        }
    }
    
    func sendPasswordReset(for email: String) async throws {
        do {
            try await Auth.auth().sendPasswordReset(withEmail: email)
        } catch {
            throw createError(code: .generalAuthError, description: error.localizedDescription, underlyingError: error)
        }
    }
    
    func signOut() async throws {
        do {
            try Auth.auth().signOut()
            GIDSignIn.sharedInstance.signOut()
        } catch {
            throw createError(code: .generalAuthError, description: error.localizedDescription, underlyingError: error)
        }
    }
}
