//
//  ApiClient.swift
//  redpill.client.ios
//

import Foundation
import os.log
import UIKit

// MARK: - Models
struct FocusStatsResponse: Codable {
    let dailyScore: Double
}

// MARK: - API client
final class ApiClient: Sendable {

    // MARK: Constants
    private let recordsSubmitEndpoint   = "https://ingestdatafunction-kwgofqfp3a-uc.a.run.app"
    private let focusStatsEndpointBase  = "https://getscoreonlyfunction-kwgofqfp3a-uc.a.run.app"
    private let batchSize               = 500
    private let session                 = URLSession.shared

    // MARK: Public API
    @discardableResult
    func submit(records: [ActivityRecord]) async -> Bool {
        AppLogger.api.info("submit(records:) called with \(records.count, privacy: .public) records")

        guard !records.isEmpty else {
            AppLogger.api.debug("No records to submit – returning `true` early")
            return true
        }

        let distinct = Array(Set(records))
        AppLogger.api.debug("De-duplicated to \(distinct.count, privacy: .public) records")

        let batches = stride(from: 0,
                             to: distinct.count,
                             by: self.batchSize).map {
            Array(distinct[$0 ..< min($0 + self.batchSize, distinct.count)])
        }

        AppLogger.api.info("Created \(batches.count, privacy: .public) batch(es) (max \(self.batchSize) each)")

        for (index, batch) in batches.enumerated() {
            let batchStart = Date()
            do {
                AppLogger.api.debug("Uploading batch \(index + 1)/\(batches.count) containing \(batch.count, privacy: .public) record(s)")
                try await upload(batch: batch)
                let duration = Date().timeIntervalSince(batchStart)
                AppLogger.api.info("Batch \(index + 1) uploaded in \(duration, format: .fixed(precision: 3)) s")
            } catch {
                AppLogger.api.error("❌ Batch \(index + 1) failed with error: \(error.localizedDescription, privacy: .public)")
                return false
            }
        }

        AppLogger.api.info("✅ All batches uploaded successfully")
        return true
    }

    func fetchFocusStats() async throws -> FocusStatsResponse {
        guard let userId = SecureStorage.userId else {
            AppLogger.api.error("fetchFocusStats failed – missing userId")
            throw URLError(.userAuthenticationRequired)
        }

        let urlString = "\(self.focusStatsEndpointBase)?userid=\(userId)"
        guard let url = URL(string: urlString) else {
            AppLogger.api.error("Malformed focus-stats URL: \(urlString, privacy: .public)")
            throw URLError(.badURL)
        }

        var req = URLRequest(url: url)
        req.httpMethod = "GET"
        req.setValue("application/json", forHTTPHeaderField: "Accept")
        req.setValue("Bearer \(try await TokenProvider.shared.validToken())",
                     forHTTPHeaderField: "Authorization")

        AppLogger.api.info("📡 GET \(urlString, privacy: .public) – requesting focus stats")

        let start = Date()
        let (data, response) = try await session.data(for: req)
        let elapsed = Date().timeIntervalSince(start)

        guard let httpResponse = response as? HTTPURLResponse else {
            AppLogger.api.error("No HTTPURLResponse received for focus stats")
            throw URLError(.badServerResponse)
        }

        AppLogger.api.info("↩️  Status \(httpResponse.statusCode, privacy: .public) – \(data.count, privacy: .public) B – \(elapsed, format: .fixed(precision: 3)) s")

        guard (200...299).contains(httpResponse.statusCode) else {
            throw URLError(.badServerResponse)
        }

        let decoder = JSONDecoder()
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        let stats = try decoder.decode(FocusStatsResponse.self, from: data)

        AppLogger.api.debug("Decoded dailyScore = \(stats.dailyScore, privacy: .public)")
        return stats
    }

    // MARK: Private helpers
    private func upload(batch: [ActivityRecord]) async throws {
        // --- FIX: Concurrently access Main-Thread-only properties safely ---
        // UIDevice is not Sendable and must be read from the MainActor.
        // We read the values into local, Sendable String constants before using them.
        let (osVersion, machineName) = await MainActor.run {
            (UIDevice.current.systemVersion, UIDevice.current.name)
        }

        let payload: [String: Any] = [
            "Client":      "redpill.client.ios",
            "Version":     Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "0.0",
            "Username":    SecureStorage.userEmail ?? "",
            "UserId":      SecureStorage.userId ?? "",
            "OSVersion":   osVersion,
            "MachineName": machineName,
            "Records":     batch.map { $0.asDictionary }
        ]

        let data = try JSONSerialization.data(withJSONObject: payload)
        guard let url = URL(string: self.recordsSubmitEndpoint) else {
            throw URLError(.badURL)
        }

        var req = URLRequest(url: url)
        req.httpMethod = "POST"
        req.httpBody = data
        req.setValue("application/json", forHTTPHeaderField: "Content-Type")
        req.setValue("Bearer \(try await TokenProvider.shared.validToken())",
                     forHTTPHeaderField: "Authorization")

        let (responseData, response) = try await session.data(for: req)

        guard let httpResponse = response as? HTTPURLResponse else {
            AppLogger.api.error("❌ No HTTPURLResponse received for records submission")
            throw URLError(.badServerResponse)
        }

        AppLogger.api.info("📡 Records submission response: Status \(httpResponse.statusCode), \(responseData.count) bytes")

        if !(200...299).contains(httpResponse.statusCode) {
            let responseString = String(data: responseData, encoding: .utf8) ?? "Unable to decode response"
            AppLogger.api.error("❌ Records submission failed with status \(httpResponse.statusCode): \(responseString)")
            throw URLError(.badServerResponse)
        }

        AppLogger.api.info("✅ Records batch submitted successfully")
    }
}
