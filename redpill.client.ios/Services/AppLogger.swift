//
//  AppLogger.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 09/06/2025.
//


//
//  Logger.swift
//  redpill.client.ios
//
//  Created by <PERSON>pert on 07/06/2025.
//
//  Provides centralized, filterable logging across the application and its extensions.
//

import Foundation
import os.log

struct AppLogger {
    /// For logs related to the app's lifecycle (scenePhase, launch, etc.).
    static let lifecycle = Logger(subsystem: "com.pravaapp.redpill", category: "Lifecycle")

    /// For logs related to data persistence, loading, and saving (ActivityStore).
    static let dataStore = Logger(subsystem: "com.pravaapp.redpill", category: "DataStore")
    
    /// For logs related to the DeviceActivity framework (monitor and report extensions).
    static let screenTime = Logger(subsystem: "com.pravaapp.redpill", category: "ScreenTime")
    
    /// For logs related to API calls and network submissions.
    static let api = Logger(subsystem: "com.pravaapp.redpill", category: "API")
    
    /// For logs related to UI interactions and view state.
    static let view = Logger(subsystem: "com.pravaapp.redpill", category: "View")
}