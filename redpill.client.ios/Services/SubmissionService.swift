//
//  SubmissionService.swift
//  redpill.client.ios
//

import Foundation
import os.log

@MainActor
class SubmissionService: ObservableObject {
    
    static let shared = SubmissionService()
    
    @Published var isSubmitting: Bool = false
    @Published var lastSuccessfulSubmission: Date?

    private let apiClient: ApiClient
    private let activityStore: CoreDataActivityStore
    private let lastSubmissionDateKey = "lastSuccessfulSubmissionDate"
    
    private var foregroundTimer: Timer?
    
    private init(apiClient: ApiClient = ApiClient(), activityStore: CoreDataActivityStore = .shared) {
        self.apiClient = apiClient
        self.activityStore = activityStore
        self.lastSuccessfulSubmission = UserDefaults.standard.object(forKey: lastSubmissionDateKey) as? Date
        AppLogger.api.info("SubmissionService singleton initialized.")
    }
    
    func startForegroundTimer() {
        stopForegroundTimer()
        let interval: TimeInterval = 300 // 5 minutes
        
        AppLogger.api.info("Starting foreground submission timer...")
        
        foregroundTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            AppLogger.api.info("Foreground timer fired.")
            Task {
                _ = await self.performSubmission()
            }
        }
        foregroundTimer?.fire()
    }
    
    func stopForegroundTimer() {
        if foregroundTimer != nil {
            AppLogger.api.info("Stopping foreground submission timer.")
            foregroundTimer?.invalidate()
            foregroundTimer = nil
        }
    }
    
    func triggerManualSubmission() {
        guard !isSubmitting else { return }
        
        Task {
            self.isSubmitting = true
            _ = await performSubmission()
            self.isSubmitting = false
        }
    }
    
    @discardableResult
    func performSubmission() async -> Bool {
        // Get unsubmitted records from Core Data
        let recordsToSubmit = await activityStore.getUnsubmittedRecords()

        guard !recordsToSubmit.isEmpty else {
            AppLogger.api.info("performSubmission: No records to submit.")
            return true
        }

        AppLogger.api.info("performSubmission: Starting submission of \(recordsToSubmit.count) records.")
        self.isSubmitting = true

        let success = await apiClient.submit(records: recordsToSubmit)

        self.isSubmitting = false

        if success {
            AppLogger.api.info("✅ Submission successful. Marking records as submitted.")
            // Mark records as submitted in Core Data
            let recordIds = recordsToSubmit.map { $0.id }
            await activityStore.markRecordsAsSubmitted(recordIds)

            let now = Date()
            self.lastSuccessfulSubmission = now
            UserDefaults.standard.set(now, forKey: lastSubmissionDateKey)
        } else {
            AppLogger.api.error("❌ Submission failed. Records will be kept for the next attempt.")
        }

        return success
    }
}
