//
//  SubmissionService.swift
//  redpill.client.ios
//

import Foundation
import os.log

@MainActor
class SubmissionService: ObservableObject {
    
    static let shared = SubmissionService()
    
    @Published var isSubmitting: Bool = false
    @Published var lastSuccessfulSubmission: Date?

    private let apiClient: ApiClient
    private let activityStore: ActivityStore
    private let lastSubmissionDateKey = "lastSuccessfulSubmissionDate"
    
    private var foregroundTimer: Timer?
    
    private init(apiClient: ApiClient = ApiClient(), activityStore: ActivityStore = .shared) {
        self.apiClient = apiClient
        self.activityStore = activityStore
        self.lastSuccessfulSubmission = UserDefaults.standard.object(forKey: lastSubmissionDateKey) as? Date
        AppLogger.api.info("SubmissionService singleton initialized.")
    }
    
    func startForegroundTimer() {
        stopForegroundTimer()
        let interval: TimeInterval = 300 // 5 minutes
        
        AppLogger.api.info("Starting foreground submission timer...")
        
        foregroundTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            AppLogger.api.info("Foreground timer fired.")
            Task {
                _ = await self.performSubmission()
            }
        }
        foregroundTimer?.fire()
    }
    
    func stopForegroundTimer() {
        if foregroundTimer != nil {
            AppLogger.api.info("Stopping foreground submission timer.")
            foregroundTimer?.invalidate()
            foregroundTimer = nil
        }
    }
    
    func triggerManualSubmission() {
        guard !isSubmitting else { return }
        
        Task {
            self.isSubmitting = true
            _ = await performSubmission()
            self.isSubmitting = false
        }
    }
    
    @discardableResult
    func performSubmission() async -> Bool {
        // Check authentication first
        guard SecureStorage.userId != nil, SecureStorage.userEmail != nil else {
            AppLogger.api.warning("⚠️ performSubmission: User not authenticated, skipping submission")
            return false
        }

        // Get all records from the single store
        await activityStore.load()
        let recordsToSubmit = activityStore.records

        guard !recordsToSubmit.isEmpty else {
            AppLogger.api.info("performSubmission: No records to submit.")
            return true
        }

        AppLogger.api.info("performSubmission: Starting submission of \(recordsToSubmit.count) records.")
        self.isSubmitting = true

        let success = await apiClient.submit(records: recordsToSubmit)

        self.isSubmitting = false

        if success {
            AppLogger.api.info("✅ Submission successful. Clearing submitted records.")

            // Clear all submitted records
            await activityStore.remove(records: recordsToSubmit)

            let now = Date()
            self.lastSuccessfulSubmission = now
            UserDefaults.standard.set(now, forKey: lastSubmissionDateKey)

            AppLogger.api.info("✅ Store cleared after successful submission.")
        } else {
            AppLogger.api.error("❌ Submission failed. Records will be kept for the next attempt.")
        }

        return success
    }
}
