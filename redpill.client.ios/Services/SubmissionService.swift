//
//  SubmissionService.swift
//  redpill.client.ios
//

import Foundation
import os.log

@MainActor
class SubmissionService: ObservableObject {
    
    static let shared = SubmissionService()
    
    @Published var isSubmitting: Bool = false
    @Published var lastSuccessfulSubmission: Date?

    private let apiClient: ApiClient
    private let activityStore: CoreDataActivityStore
    private let lastSubmissionDateKey = "lastSuccessfulSubmissionDate"
    
    private var foregroundTimer: Timer?
    
    private init(apiClient: ApiClient = ApiClient(), activityStore: CoreDataActivityStore = .shared) {
        self.apiClient = apiClient
        self.activityStore = activityStore
        self.lastSuccessfulSubmission = UserDefaults.standard.object(forKey: lastSubmissionDateKey) as? Date
        AppLogger.api.info("SubmissionService singleton initialized.")
    }
    
    func startForegroundTimer() {
        stopForegroundTimer()
        let interval: TimeInterval = 300 // 5 minutes
        
        AppLogger.api.info("Starting foreground submission timer...")
        
        foregroundTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            AppLogger.api.info("Foreground timer fired.")
            Task {
                _ = await self.performSubmission()
            }
        }
        foregroundTimer?.fire()
    }
    
    func stopForegroundTimer() {
        if foregroundTimer != nil {
            AppLogger.api.info("Stopping foreground submission timer.")
            foregroundTimer?.invalidate()
            foregroundTimer = nil
        }
    }
    
    func triggerManualSubmission() {
        guard !isSubmitting else { return }
        
        Task {
            self.isSubmitting = true
            _ = await performSubmission()
            self.isSubmitting = false
        }
    }
    
    @discardableResult
    func performSubmission() async -> Bool {
        // Check authentication first
        guard SecureStorage.userId != nil, SecureStorage.userEmail != nil else {
            AppLogger.api.warning("⚠️ performSubmission: User not authenticated, skipping submission")
            return false
        }

        // Get records from both stores
        let coreDataRecords = await activityStore.getUnsubmittedRecords()
        let legacyRecords = ActivityStore.shared.records

        // Combine all records for submission
        var allRecords = coreDataRecords
        allRecords.append(contentsOf: legacyRecords)

        guard !allRecords.isEmpty else {
            AppLogger.api.info("performSubmission: No records to submit.")
            return true
        }

        AppLogger.api.info("performSubmission: Starting submission of \(allRecords.count) records (\(coreDataRecords.count) from Core Data, \(legacyRecords.count) from legacy store).")
        self.isSubmitting = true

        let success = await apiClient.submit(records: allRecords)

        self.isSubmitting = false

        if success {
            AppLogger.api.info("✅ Submission successful. Clearing submitted records from both stores.")

            // Mark Core Data records as submitted
            if !coreDataRecords.isEmpty {
                let coreDataRecordIds = coreDataRecords.map { $0.id }
                await activityStore.markRecordsAsSubmitted(coreDataRecordIds)
                // Refresh Core Data store to update UI
                await activityStore.loadRecentRecords()
            }

            // Clear legacy store records
            if !legacyRecords.isEmpty {
                await ActivityStore.shared.remove(records: legacyRecords)
                // Refresh legacy store to update UI
                await ActivityStore.shared.load()
            }

            let now = Date()
            self.lastSuccessfulSubmission = now
            UserDefaults.standard.set(now, forKey: lastSubmissionDateKey)

            AppLogger.api.info("✅ Stores refreshed after successful submission.")
        } else {
            AppLogger.api.error("❌ Submission failed. Records will be kept for the next attempt.")
        }

        return success
    }
}
