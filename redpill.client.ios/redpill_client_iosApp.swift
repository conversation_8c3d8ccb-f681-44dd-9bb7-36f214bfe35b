//
//  redpill_client_iosApp.swift
//  redpill.client.ios
//
//  Uses the iOS 18-era `onChange` overloads (no deprecation warnings).
//  Report-extension data are funnelled through DeviceActivityReport’s
//  configuration closure, then persisted in the host-app sandbox.
//

import SwiftUI
import FirebaseCore          // Firebase initialisation
import FamilyControls        // Screen-Time auth API
import DeviceActivity        // Monitoring & reports

@main
struct RedpillClientIOSApp: App {

    // ───────────── System delegates ─────────────
    @UIApplicationDelegateAdaptor(AppDelegate.self)
    private var appDelegate

    // ───────────── View-models / singletons ─────
    @StateObject private var authViewModel     = AuthViewModel()
    @StateObject private var activityMonitor   = ActivityMonitor()
    @StateObject private var activityStore     = CoreDataActivityStore.shared
    @StateObject private var submissionService = SubmissionService.shared
    @StateObject private var focusViewModel    = FocusViewModel()

    // Receives Base-64 payloads from the report extension
    @State private var configurationString = ""
    @State private var reportRefreshKey = UUID()

    @Environment(\.scenePhase) private var scenePhase

    // ───────────── Init ─────────────────────────
    init() {
        FirebaseApp.configure()                              // ✅ SDK boot :contentReference[oaicite:0]{index=0}
    }

    // ───────────── Scene graph ──────────────────
    var body: some Scene {
        WindowGroup {
            rootView
                .preferredColorScheme(.dark)                 // app-wide dark mode
                .environmentObject(authViewModel)
                .environmentObject(activityMonitor)
                .environmentObject(activityStore)
                .environmentObject(submissionService)
                .environmentObject(focusViewModel)
                .background(reportTriggerView)               // 1 × 1 invisible
                .task { await setupMonitoring() }            // Screen-Time auth
                .onChange(of: scenePhase) { _, newPhase in                // new
                    handleScenePhaseChange(newPhase)
                }
                .onChange(of: configurationString) { _, newString in      // new
                    decodeAndPersist(newString)
                }
        }
    }
}

// MARK: – Sub-views ------------------------------------------------------------

private extension RedpillClientIOSApp {

    /// Root navigation
    @ViewBuilder var rootView: some View {
        if authViewModel.isSignedIn {
            ContentView()
        } else {
            LoginView()
        }
    }

    var reportTriggerView: some View {
        VStack {
            // Trigger both report contexts to ensure data collection
            DeviceActivityReport(.totalActivity)
                .frame(width: 1, height: 1)
                .opacity(0.0001)
                .id("totalActivity-\(reportRefreshKey)")

            DeviceActivityReport(DeviceActivityReport.Context("GlobalUsage"))
                .frame(width: 1, height: 1)
                .opacity(0.0001)
                .id("globalUsage-\(reportRefreshKey)")
        }
        .onAppear {
            // Start periodic report refresh
            startReportRefreshTimer()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshReports"))) { _ in
            AppLogger.screenTime.info("Manual report refresh triggered")
            reportRefreshKey = UUID()
        }
    }
}

// MARK: – Screen-Time monitoring / scene-phase --------------------------------

private extension RedpillClientIOSApp {

    /// Requests Screen-Time permission once, then starts monitoring.
    func setupMonitoring() async {
        do {
            let status = AuthorizationCenter.shared.authorizationStatus
            AppLogger.screenTime.info("Initial Screen Time authorization status: \(status.rawValue)")
            
            if status == .notDetermined {
                AppLogger.screenTime.info("Requesting Screen Time authorization...")
                try await AuthorizationCenter.shared.requestAuthorization(for: .individual)
                AppLogger.screenTime.info("Authorization request completed, new status: \(AuthorizationCenter.shared.authorizationStatus.rawValue)")
            }
            
            guard AuthorizationCenter.shared.authorizationStatus == .approved else {
                AppLogger.screenTime.error("Screen Time authorization not approved. Current status: \(AuthorizationCenter.shared.authorizationStatus.rawValue)")
                return
            }
            
            AppLogger.screenTime.info("Starting activity monitoring...")
            await activityMonitor.startMonitoring()
        } catch {
            AppLogger.screenTime.error("Screen Time authorization failed: \(error.localizedDescription)")
        }
    }

    /// Scene-phase transitions: timers, BGTasks, etc.
    func handleScenePhaseChange(_ phase: ScenePhase) {
        switch phase {
        case .active:
            activityMonitor.appDidBecomeActive()
            Task {
                await activityStore.load()
                // Also load legacy store to check for new data from extensions
                await ActivityStore.shared.load()
                AppLogger.lifecycle.info("App became active - loaded both stores")
            }
            if authViewModel.isSignedIn {
                focusViewModel.startAutomaticRefresh()
                submissionService.startForegroundTimer()
            }

        case .background:
            activityMonitor.appDidEnterBackground()
            focusViewModel.stopAutomaticRefresh()
            submissionService.stopForegroundTimer()
            appDelegate.scheduleAppRefresh()               // BGProcessing :contentReference[oaicite:3]{index=3}

        case .inactive:
            submissionService.stopForegroundTimer()

        @unknown default:
            break
        }
    }

    /// Converts Base-64 → `[ActivityRecord]`, then writes via `ActivityStore`.
    func decodeAndPersist(_ payload: String) {
        Task.detached(priority: .utility) {
            guard
                let data    = Data(base64Encoded: payload),
                let records = try? JSONDecoder().decode([ActivityRecord].self, from: data)
            else { return }
            await CoreDataActivityStore.shared.append(contentsOf: records)
        }
    }

    /// Starts a timer to periodically refresh reports to trigger data collection
    func startReportRefreshTimer() {
        Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { _ in // 5 minutes instead of 1
            AppLogger.screenTime.info("Refreshing reports to trigger data collection...")
            Task { @MainActor in
                reportRefreshKey = UUID()
            }
        }
    }
}
