<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleVersion</key>
    <string>$(CURRENT_PROJECT_VERSION)</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>$(PRODUCT_BUNDLE_PACKAGE_type)</string>
    
    <key>UIApplicationSceneManifest</key>
    <dict>
        <key>UIApplicationSupportsMultipleScenes</key>
        <false/>
        <key>UISceneConfigurations</key>
        <dict>
            <key>UIWindowSceneSessionRoleApplication</key>
            <array>
                <dict>
                    <key>UISceneConfigurationName</key>
                    <string>Default Configuration</string>
                    <key>UISceneDelegateClassName</key>
                    <string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
                </dict>
            </array>
        </dict>
    </dict>

    <key>NSFamilyControlsUsageDescription</key>
    <string>We need access to monitor app usage for productivity tracking.</string>
    
    <key>BGTaskSchedulerPermittedIdentifiers</key>
    <array>
        <string>com.redpill.client.ios.submitRecords</string>
    </array>
    <key>UIBackgroundModes</key>
    <array>
        <string>processing</string>
    </array>

    <key>EXReportExtension</key>
    <dict>
        <key>TotalActivity</key>
        <dict>
            <key>DEXIdentifier</key>
            <string>com.redpill.client.ios.report</string>
        </dict>
        <key>GlobalUsage</key>
        <dict>
            <key>DEXIdentifier</key>
            <string>com.redpill.client.ios.report</string>
        </dict>
    </dict>
    </dict>
</plist>
