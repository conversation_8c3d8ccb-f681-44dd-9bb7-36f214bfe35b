//
//  AppDelegate.swift
//  redpill.client.ios
//
import UIKit
import GoogleSignIn
import BackgroundTasks
import os.log

@MainActor
class AppDelegate: NSObject, UIApplicationDelegate {
    
    // The identifier MUST match the one in your Info.plist
    let backgroundTaskIdentifier = "com.redpill.client.ios.submitRecords"

    func application(_ application: UIApplication,
                     didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        // Registering the background task should be the only thing we do here for this system.
        // It's a one-time setup call.
        registerBackgroundTask()
        
        return true
    }
    
    func application(_ app: UIApplication,
                     open url: URL,
                     options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        return GIDSignIn.sharedInstance.handle(url)
    }
    
    // MARK: - Background Task Management
    
    private func registerBackgroundTask() {
        let success = BGTaskScheduler.shared.register(forTaskWithIdentifier: backgroundTaskIdentifier, using: nil) { task in
            // The OS calls this closure when it's time to run your task.
            // We ensure it's the correct type.
            guard let processingTask = task as? BGProcessingTask else {
                AppLogger.lifecycle.error("🔴 Received an unknown background task type.")
                task.setTaskCompleted(success: false)
                return
            }
            // Call the handler function.
            self.handleAppRefresh(task: processingTask)
        }
        
        if success {
            AppLogger.lifecycle.info("✅ Successfully registered background task: \(self.backgroundTaskIdentifier)")
        } else {
            // This is the error you were seeing. If this fails, scheduling will never work.
            AppLogger.lifecycle.error("🔴 Failed to register background task. Check your Info.plist and entitlements.")
        }
    }
    
    public func scheduleAppRefresh() {
        let request = BGProcessingTaskRequest(identifier: backgroundTaskIdentifier)
        request.requiresNetworkConnectivity = true
        request.requiresExternalPower = false
        
        #if DEBUG
        request.earliestBeginDate = Date(timeIntervalSinceNow: 60) // In 1 minute for testing
        AppLogger.lifecycle.info("Scheduling background task for DEBUG.")
        #else
        request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // In 15 minutes for release
        AppLogger.lifecycle.info("Scheduling background task for RELEASE.")
        #endif
        
        do {
            try BGTaskScheduler.shared.submit(request)
            AppLogger.lifecycle.info("✅ Successfully submitted background task request.")
        } catch {
            // With a successful registration, this error should no longer occur.
            AppLogger.lifecycle.error("🔴 Could not schedule app refresh task: \(error.localizedDescription)")
        }
    }
    
    private func handleAppRefresh(task: BGProcessingTask) {
        // Important: Schedule the next refresh right away.
        scheduleAppRefresh()
        
        task.expirationHandler = {
            AppLogger.lifecycle.warning("⚠️ Background task expired.")
            task.setTaskCompleted(success: false)
        }
        
        AppLogger.lifecycle.info("▶️ Starting background task execution.")
        
        Task {
            // The background task calls the same shared service.
            let success = await SubmissionService.shared.performSubmission()
            AppLogger.lifecycle.info("⏹️ Background task finished with success: \(success)")
            task.setTaskCompleted(success: success)
        }
    }
}
