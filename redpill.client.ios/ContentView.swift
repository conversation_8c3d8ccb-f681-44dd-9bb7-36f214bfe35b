import SwiftUI
import DeviceActivity
import FamilyControls

struct ContentView: View {

    // Type-safe tag values for the TabView
    private enum Tab: Hashable { case dashboard, activity, stats, settings }

    @State private var selectedTab: Tab = .dashboard
    @EnvironmentObject private var activityMonitor: ActivityMonitor

    // MARK: – Report configuration
    private let reportContext = DeviceActivityReport.Context(rawValue: "TotalActivity")

    private var todayFilter: DeviceActivityFilter {
        let today = Calendar.current.dateInterval(of: .day, for: .now)!
        return DeviceActivityFilter(
            segment: .daily(during: today),
            users:   .all,
            devices: .init([.iPhone])
        )
    }

    // Add this to ContentView to process report data
    private var totalActivityReport: some View {
        DeviceActivityReport(reportContext, filter: todayFilter)
            .frame(width: 0, height: 0)
            .opacity(0) // Hide the view since we're just processing data
    }

    // MARK: – View hierarchy
    var body: some View {
        TabView(selection: $selectedTab) {

            // ─────────── Dashboard ───────────
            NavigationStack {
                DashboardView()          // ← shows live ActivityRecord list
            }
            .tabItem { Label("Dashboard", systemImage: "house") }
            .tag(Tab.dashboard)

            // ─────────── Activity Report ───────────
            NavigationStack {
                ActivityReportView(
                    context: reportContext,
                    baseFilter: todayFilter
                )
                .navigationTitle("Activity")
            }
            .tabItem { Label("Activity", systemImage: "chart.bar") }
            .tag(Tab.activity)

            // ─────────── Stats placeholder ───────────
            NavigationStack {
                Text("Stats")
                    .navigationTitle("Stats")
            }
            .tabItem { Label("Stats", systemImage: "chart.pie") }
            .tag(Tab.stats)

            // ─────────── Settings placeholder ───────────
            NavigationStack {
                Text("Settings")
                    .navigationTitle("Settings")
            }
            .tabItem { Label("Settings", systemImage: "gear") }
            .tag(Tab.settings)

            // Add the hidden report processor
            totalActivityReport
        }
        .onAppear {
            // Request authorization when the app launches
            Task {
                do {
                    try await AuthorizationCenter.shared.requestAuthorization(for: .individual)
                    await activityMonitor.startMonitoring()
                } catch {
                    print("Failed to request authorization: \(error.localizedDescription)")
                }
            }
        }
    }
}
