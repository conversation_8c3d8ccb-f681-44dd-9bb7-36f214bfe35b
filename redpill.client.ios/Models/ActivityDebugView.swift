//
//  ActivityDebugView.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 18/06/2025.
//


//  ActivityDebugView.swift
//  ⟡ New SwiftUI view — **App target only**

import SwiftUI

struct ActivityDebugView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var store   = CoreDataActivityStore.shared
    @StateObject private var usageVM = UsageViewModel()

    var body: some View {
        NavigationStack {
            VStack {
                HStack {
                    Text("Today: \(usageVM.todayMinutes)m")
                        .font(.headline)
                    Spacer()
                    Button("Log in-app Interaction") {
                        Task { await store.logInAppInteraction() }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding(.horizontal)

                List(store.records) { rec in
                    VStack(alignment: .leading, spacing: 2) {
                        Text(rec.source).bold()
                        if let end = rec.end {
                            Text(
                                Date(timeIntervalSince1970: Double(end)/1000),
                                style: .time)
                            .font(.caption2)
                        }
                        Text("Duration: \(formatDuration(start: rec.begin, end: rec.end))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("Activity Records")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") { dismiss() }
                }
                
                ToolbarItem(placement: .primaryAction) {
                    Button("Generate Test Data") {
                        Task { await store.addMockDataForUITesting() }
                    }
                }
            }
        }
    }
    
    private func formatDuration(start: Int64, end: Int64?) -> String {
        guard let end = end else { return "ongoing" }
        let durationSeconds = Double(end - start) / 1000.0
        let minutes = Int(durationSeconds / 60)
        let seconds = Int(durationSeconds.truncatingRemainder(dividingBy: 60))
        return "\(minutes)m \(seconds)s"
    }
}
