//
//  ActivityRecord.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//
//  Canonical data model for a single tracked activity.
//  CRITICAL: Ensure this file is a member of all targets (App, Report Extension, Monitor Extension).
//

import Foundation

struct ActivityRecord: Codable, Hashable, Identifiable {
    let id: UUID
    let source: String
    let subtype: String
    let begin: Int64
    var end: Int64?
    let userTimezone: Int
    var workingIntensity: Double

    // Coding keys to match server and file data
    enum CodingKeys: String, CodingKey {
        case source = "Source"
        case subtype = "Subtype"
        case begin = "Begin"
        case end = "End"
        case userTimezone = "UserTimezone"
        case workingIntensity = "WorkingIntensity"
    }

    // Custom initializer to assign a UUID on creation
    init(id: UUID = UUID(), source: String, subtype: String, begin: Int64, end: Int64? = nil, userTimezone: Int, workingIntensity: Double) {
        self.id = id
        self.source = source
        self.subtype = subtype
        self.begin = begin
        self.end = end
        self.userTimezone = userTimezone
        self.workingIntensity = workingIntensity
    }

    // FIX: Implement init(from:) to satisfy Decodable conformance.
    // This initializer is called when decoding JSON that does not contain an 'id'.
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Decode all properties from the container
        source = try container.decode(String.self, forKey: .source)
        subtype = try container.decode(String.self, forKey: .subtype)
        begin = try container.decode(Int64.self, forKey: .begin)
        end = try container.decodeIfPresent(Int64.self, forKey: .end)
        userTimezone = try container.decode(Int.self, forKey: .userTimezone)
        workingIntensity = try container.decode(Double.self, forKey: .workingIntensity)
        
        // Assign a new, unique ID since one doesn't exist in the decoded data
        id = UUID()
    }
    
    // Convert to dictionary for API submission
    var asDictionary: [String: Any] {
        [
            "Source": source,
            "Subtype": subtype,
            "Begin": begin,
            "End": end ?? begin,
            "UserTimezone": userTimezone,
            "WorkingIntensity": workingIntensity
        ]
    }
}
