//
//  CoreDataStack.swift
//  redpill.client.ios
//
//  Core Data stack for efficient activity record persistence
//

import CoreData
import Foundation
import os.log

final class CoreDataStack: @unchecked Sendable {
    static let shared = CoreDataStack()
    
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "CoreDataStack")
    
    private init() {}
    
    // MARK: - Core Data Stack
    
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "ActivityModel")
        
        // Configure for App Group sharing
        if let storeURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: PravaIDs.appGroup)?.appendingPathComponent("ActivityModel.sqlite") {
            let description = NSPersistentStoreDescription(url: storeURL)
            description.shouldInferMappingModelAutomatically = true
            description.shouldMigrateStoreAutomatically = true
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
            container.persistentStoreDescriptions = [description]
        }
        
        container.loadPersistentStores { [weak self] _, error in
            if let error = error {
                self?.log.error("Core Data failed to load: \(error.localizedDescription)")
                fatalError("Core Data failed to load: \(error)")
            } else {
                self?.log.info("Core Data loaded successfully")
            }
        }
        
        container.viewContext.automaticallyMergesChangesFromParent = true
        return container
    }()
    
    var context: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    // MARK: - Save Context
    
    func save() {
        let context = persistentContainer.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
                log.debug("Core Data context saved successfully")
            } catch {
                log.error("Failed to save Core Data context: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Background Context

    func performBackgroundTask<T: Sendable>(_ block: @escaping @Sendable (NSManagedObjectContext) -> T) async -> T {
        return await withCheckedContinuation { continuation in
            persistentContainer.performBackgroundTask { context in
                let result = block(context)
                continuation.resume(returning: result)
            }
        }
    }
}
