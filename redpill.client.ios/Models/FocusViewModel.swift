//
//  FocusViewModel.swift
//  redpill.client.ios
//

import Foundation
import os.log

@MainActor // <-- THE FIX
class FocusViewModel: ObservableObject {
    @Published var dailyScore: Double = 0.0
    @Published var dailyScoreMax: Double = 100.0
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private let apiClient: ApiClient
    
    init(apiClient: ApiClient = ApiClient()) {
        self.apiClient = apiClient
    }
    
    func startAutomaticRefresh() {
        Task {
            await fetchScore()
        }
    }
    
    func stopAutomaticRefresh() {}
    
    func fetchScore() async {
        // Now that the class is @MainActor, all of these property
        // mutations are guaranteed to be on the main thread.
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await apiClient.fetchFocusStats()
            self.dailyScore = response.dailyScore
            self.dailyScoreMax = 100.0 // Assuming this is constant
        } catch {
            self.errorMessage = error.localizedDescription
        }
        
        self.isLoading = false
    }
}
