//
//  CoreDataActivityStore.swift
//  redpill.client.ios
//
//  More efficient Core Data-based activity record persistence
//

import CoreData
import Foundation
import os.log
import SwiftUI

@MainActor
final class CoreDataActivityStore: ObservableObject {
    static let shared = CoreDataActivityStore()
    
    @Published private(set) var records: [ActivityRecord] = []
    @Published private(set) var recordCount: Int = 0
    
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "CoreDataActivityStore")
    private let coreDataStack = CoreDataStack.shared
    
    private init() {
        Task {
            await loadRecentRecords()
        }
    }
    
    // MARK: - Public Interface
    
    nonisolated func append(contentsOf newRecords: [ActivityRecord]) async {
        guard !newRecords.isEmpty else { return }
        
        log.info("Saving \(newRecords.count) records to Core Data")
        
        await coreDataStack.performBackgroundTask { context in
            for record in newRecords {
                let entity = ActivityRecordEntity(context: context)
                entity.id = record.id
                entity.source = record.source
                entity.subtype = record.subtype
                entity.begin = record.begin
                entity.end = record.end ?? 0
                entity.userTimezone = Int32(record.userTimezone)
                entity.workingIntensity = record.workingIntensity
                entity.submitted = false
            }
            
            do {
                try context.save()
                self.log.info("Successfully saved \(newRecords.count) records to Core Data")
            } catch {
                self.log.error("Failed to save records to Core Data: \(error.localizedDescription)")
            }
        }
        
        // Update UI on main thread
        await MainActor.run {
            self.records.append(contentsOf: newRecords)
            self.recordCount += newRecords.count
            
            // Keep only recent records in memory
            if self.records.count > 1000 {
                self.records = Array(self.records.suffix(1000))
            }
        }
    }
    
    func loadRecentRecords() async {
        let fetchedRecords = await coreDataStack.performBackgroundTask { context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ActivityRecordEntity.begin, ascending: false)]
            request.fetchLimit = 1000
            
            do {
                let entities = try context.fetch(request)
                return entities.compactMap { entity in
                    ActivityRecord(
                        id: entity.id ?? UUID(),
                        source: entity.source ?? "",
                        subtype: entity.subtype ?? "",
                        begin: entity.begin,
                        end: entity.end == 0 ? nil : entity.end,
                        userTimezone: Int(entity.userTimezone),
                        workingIntensity: entity.workingIntensity
                    )
                }
            } catch {
                self.log.error("Failed to fetch records: \(error.localizedDescription)")
                return []
            }
        }
        
        await MainActor.run {
            self.records = fetchedRecords
            self.recordCount = fetchedRecords.count
            self.log.info("Loaded \(fetchedRecords.count) recent records from Core Data")
        }
    }
    
    // MARK: - Submission Support
    
    nonisolated func getUnsubmittedRecords() async -> [ActivityRecord] {
        return await coreDataStack.performBackgroundTask { context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "submitted == NO")
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ActivityRecordEntity.begin, ascending: true)]
            
            do {
                let entities = try context.fetch(request)
                return entities.compactMap { entity in
                    ActivityRecord(
                        id: entity.id ?? UUID(),
                        source: entity.source ?? "",
                        subtype: entity.subtype ?? "",
                        begin: entity.begin,
                        end: entity.end == 0 ? nil : entity.end,
                        userTimezone: Int(entity.userTimezone),
                        workingIntensity: entity.workingIntensity
                    )
                }
            } catch {
                self.log.error("Failed to fetch unsubmitted records: \(error.localizedDescription)")
                return []
            }
        }
    }
    
    nonisolated func markRecordsAsSubmitted(_ recordIds: [UUID]) async {
        await coreDataStack.performBackgroundTask { context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "id IN %@", recordIds)
            
            do {
                let entities = try context.fetch(request)
                for entity in entities {
                    entity.submitted = true
                }
                try context.save()
                self.log.info("Marked \(entities.count) records as submitted")
            } catch {
                self.log.error("Failed to mark records as submitted: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Debug Methods
    
    func addMockDataForUITesting() async {
        let now = Date()
        
        let mockApps = [
            ("debug_com.apple.mobilesafari", "Safari"),
            ("debug_com.apple.mobilemail", "Mail"),
            ("debug_com.apple.MobileSMS", "Messages"),
            ("debug_com.apple.Music", "Music"),
            ("debug_com.apple.camera", "Camera"),
            ("debug_com.apple.mobilenotes", "Notes"),
            ("debug_com.apple.weather", "Weather"),
            ("debug_com.apple.Maps", "Maps")
        ]
        
        var mockRecords: [ActivityRecord] = []
        
        for (bundleId, _) in mockApps {
            let sessionCount = Int.random(in: 3...5)
            
            for _ in 0..<sessionCount {
                let hoursAgo = Double.random(in: 0.5...8.0)
                let sessionDuration = Double.random(in: 30...600)
                
                let endTime = now.addingTimeInterval(-hoursAgo * 3600)
                let startTime = endTime.addingTimeInterval(-sessionDuration)
                
                let record = ActivityRecord(
                    source: bundleId,
                    subtype: "app.usage",
                    begin: Int64(startTime.timeIntervalSince1970 * 1000),
                    end: Int64(endTime.timeIntervalSince1970 * 1000),
                    userTimezone: TimeZone.current.secondsFromGMT() / 60,
                    workingIntensity: 1.0
                )
                
                mockRecords.append(record)
            }
        }
        
        mockRecords.sort { $0.begin < $1.begin }
        log.info("Generated \(mockRecords.count) mock activity records")
        await append(contentsOf: mockRecords)
    }
    
    nonisolated func logInAppInteraction() async {
        let now = Int64(Date().timeIntervalSince1970 * 1000)
        let tz = TimeZone.current.secondsFromGMT() / 60
        
        let record = ActivityRecord(
            source: "debug_pravaapp",
            subtype: "inapp.interaction",
            begin: now,
            end: now + 1000,
            userTimezone: tz,
            workingIntensity: 0.0
        )
        
        await append(contentsOf: [record])
    }
    
    func debugRecordCount() async {
        let totalCount = await coreDataStack.performBackgroundTask { context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            return (try? context.count(for: request)) ?? 0
        }
        
        let unsubmittedCount = await coreDataStack.performBackgroundTask { context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "submitted == NO")
            return (try? context.count(for: request)) ?? 0
        }
        
        await MainActor.run {
            log.info("Core Data: \(totalCount) total records, \(unsubmittedCount) unsubmitted")
            log.info("Memory: \(self.records.count) records loaded")
        }
    }
}
