//
//  ActivityStore.swift — shared between app & extensions
//

import Foundation
import os.log

@MainActor
final class ActivityStore: ObservableObject {

    // MARK: – Single-ton
    static let shared = ActivityStore() 

    @Published private(set) var records: [ActivityRecord] = []

    // Memory management: Limit records in memory to prevent crashes
    private let maxRecordsInMemory = 100 // Increased limit for better data visibility

    // MARK: – File locations
    let fileURL: URL
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "ActivityStore")

    // Re-use the same encoders everywhere to avoid per-call allocations.
    static let encoder = JSONEncoder()
    static let decoder = JSONDecoder()

    private init() {
        guard let containerURL = FileManager.default
                .containerURL(forSecurityApplicationGroupIdentifier: PravaIDs.appGroup) else {
            fatalError("App-Group container missing")
        }

        // ◎ Use the container root — simpler permissions than Library/Caches
        self.fileURL = containerURL.appendingPathComponent("activity_records.json")

        // Ensure the file exists and is writable
        self.ensureFileExists()

        #if !MONITOR_EXTENSION            // ⟵ monitor must NOT load everything
        // Start a background load that can be cancelled if the process is memory-constrained.
        Task.detached { await self.load() }  // Add explicit 'self' here
        #endif
    }

    // MARK: - File Management
    private func ensureFileExists() {
        let fileManager = FileManager.default

        // Check if file exists
        if !fileManager.fileExists(atPath: fileURL.path) {
            log.info("Creating activity records file at: \(self.fileURL.path)")

            // Try to create the file with proper permissions
            let success = fileManager.createFile(atPath: fileURL.path, contents: Data(), attributes: [
                .posixPermissions: 0o666  // Read/write for owner, group, and others
            ])

            if success {
                log.info("Successfully created activity records file")
            } else {
                log.error("Failed to create activity records file")
            }
        } else {
            // File exists, check if it's writable
            if fileManager.isWritableFile(atPath: fileURL.path) {
                log.info("Activity records file exists and is writable")
            } else {
                log.warning("Activity records file exists but is not writable, attempting to fix permissions")

                // Try to fix permissions
                do {
                    try fileManager.setAttributes([.posixPermissions: 0o666], ofItemAtPath: fileURL.path)
                    log.info("Fixed file permissions")
                } catch {
                    log.error("Failed to fix file permissions: \(error.localizedDescription)")
                }
            }
        }
    }

    private nonisolated func ensureFileExistsAsync() async {
        let fileManager = FileManager.default

        if !fileManager.fileExists(atPath: fileURL.path) {
            log.info("File doesn't exist, creating it...")
            let success = fileManager.createFile(atPath: fileURL.path, contents: Data(), attributes: [
                .posixPermissions: 0o666
            ])
            if !success {
                log.error("Failed to create file in async context")
            }
        }
    }

    private nonisolated func appendUsingDataWrite(_ newRecords: [ActivityRecord]) async {
        do {
            // Read existing content
            var existingData = Data()
            if FileManager.default.fileExists(atPath: fileURL.path) {
                existingData = try Data(contentsOf: fileURL)
            }

            // Prepare new data
            var newData = existingData
            for rec in newRecords {
                let recordData = try await Self.encoder.encode(rec)
                newData.append(recordData)
                newData.append(Data("\n".utf8))
            }

            // Write all data at once
            try newData.write(to: fileURL, options: .atomic)
            log.info("Successfully appended records using Data.write method")

        } catch {
            log.error("Data.write method also failed: \(error.localizedDescription)")

            // Last resort: try to save to a different location and log the issue
            await saveToFallbackLocation(newRecords)
        }
    }

    private nonisolated func saveToFallbackLocation(_ newRecords: [ActivityRecord]) async {
        // Try saving to Documents directory as a fallback
        do {
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let fallbackURL = documentsPath.appendingPathComponent("fallback_activity_records.json")

            var allData = Data()
            for rec in newRecords {
                let recordData = try await Self.encoder.encode(rec)
                allData.append(recordData)
                allData.append(Data("\n".utf8))
            }

            try allData.write(to: fallbackURL, options: .atomic)
            log.info("Saved \(newRecords.count) records to fallback location: \(fallbackURL.path)")

        } catch {
            log.error("Even fallback save failed: \(error.localizedDescription)")
        }
    }

    // MARK: – Appending
    nonisolated func append(contentsOf newRecords: [ActivityRecord]) async {
        guard !newRecords.isEmpty else { return }

        log.info("Attempting to append \(newRecords.count) records to file at \(self.fileURL.path)")

        // First, ensure the file exists and has proper permissions
        await ensureFileExistsAsync()

        // Try the standard FileHandle approach first with memory management
        do {
            let fh = try FileHandle(forWritingTo: self.fileURL)
            try fh.seekToEnd()

            // Use autoreleasepool to prevent memory accumulation
            for rec in newRecords {
                try autoreleasepool {
                    try fh.write(contentsOf: Self.encoder.encode(rec))
                    try fh.write(contentsOf: Data("\n".utf8))
                }
            }
            try fh.close()
            log.info("Successfully appended records to file using FileHandle")
        } catch {
            log.warning("FileHandle approach failed: \(error.localizedDescription), trying alternative method")

            // Fallback: Try using Data.write directly
            await appendUsingDataWrite(newRecords)
        }
        
        #if !MONITOR_EXTENSION
        await MainActor.run {
            records.append(contentsOf: newRecords)
            // Conservative memory limit for main app (extensions use even less)
            if records.count > maxRecordsInMemory {
                let excessCount = records.count - maxRecordsInMemory
                records.removeFirst(excessCount) // Remove oldest records
                log.info("Trimmed \(excessCount) old records from memory to prevent crashes")
            }
        }
        #endif

        await enforceSizeLimit()
    }


    // MARK: – Remove
    public func remove(records submittedRecords: [ActivityRecord]) async {
        let submittedIDs = Set(submittedRecords.map(\.id))
        let survivors    = self.records.filter { !submittedIDs.contains($0.id) }

        guard survivors.count != self.records.count else {
            log.info("No records to remove, store already in sync.")
            return
        }

        records = survivors
        log.info("Removed \(submittedRecords.count) records from in-memory store.")

        // Overwrite with survivors – streamed to avoid a big temporary buffer.
        do {
            let handle = try FileHandle(forWritingTo: fileURL)
            try handle.truncate(atOffset: 0)

            for rec in survivors.reversed() {                       // oldest → newest
                let line = try Self.encoder.encode(rec)
                try handle.write(contentsOf: line)
                try handle.write(contentsOf: Data("\n".utf8))
            }
            try handle.close()
        } catch {
            log.error("Failed to overwrite store: \(error.localizedDescription)")
        }
    }

    // MARK: – Fast aggregate (already streamed)
    nonisolated func reduceTodayTotalSeconds() async throws -> Double {
        let today = Calendar.current.dateInterval(of: .day, for: .now)!
        let fh    = try FileHandle(forReadingFrom: fileURL)
        var acc   = 0.0

        for try await line in fh.bytes.lines {                      // async sequence
            guard let data = line.data(using: .utf8) else { continue }
            guard let rec  = try? await Self.decoder.decode(ActivityRecord.self, from: data) else { continue }
            guard let end  = rec.end else { continue }

            let beginDate = Date(timeIntervalSince1970: Double(rec.begin)/1_000)
            guard today.contains(beginDate) else { continue }

            acc += Double(end - rec.begin)/1_000
        }
        try fh.close()
        return acc
    }

    // MARK: – Clear
    public func clearAll() async {
        do {
            try FileManager.default.removeItem(at: fileURL)
            records = []
            log.info("Cleared all records from the store.")
        } catch let error as NSError where error.code == NSFileNoSuchFileError {
            records = []                                           // nothing to clear
        } catch {
            log.error("Failed to clear store: \(error.localizedDescription)")
        }
    }

    // MARK: – Streamed load (NEW)
    /// Loads *at most* the last `maxLines` records into memory.
    /// Pass `nil` to load everything (only the host app does this).
    public func load(maxLines: Int? = nil) async {
        // Use conservative limit for memory management
        let effectiveLimit = maxLines ?? maxRecordsInMemory
        // Keep a small local buffer so the extension never balloons in RAM ≈ WWDC guidance.
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            await MainActor.run { self.records = [] }
            return
        }

        var buffer: [ActivityRecord] = []
        do {
            let fh = try FileHandle(forReadingFrom: fileURL)

            // Iterate **oldest → newest** so we can cap by line-count easily.
            for try await line in fh.bytes.lines {
                autoreleasepool {
                    guard let data = line.data(using: .utf8),
                          let rec  = try? Self.decoder.decode(ActivityRecord.self, from: data)
                    else { return }

                    buffer.append(rec)
                    if buffer.count > effectiveLimit {
                        buffer.removeFirst(buffer.count - effectiveLimit)     // drop oldest overflow
                    }
                }
            }
            try fh.close()
        } catch {
            log.error("Stream-load failed: \(error.localizedDescription)")
        }

        await MainActor.run {
            self.records = buffer.sorted { $0.begin > $1.begin }
            log.info("Stream-loaded \(self.records.count) records into memory.")
        }
    }

    // ───────────── File-size guard (≤ 6 MiB) ─────────────
    private static let hardLimit: UInt64 = 6 * 1_048_576     // 6 MiB

    private func enforceSizeLimit() async {
        guard
            let attrs = try? FileManager.default.attributesOfItem(atPath: fileURL.path),
            let size  = attrs[.size] as? UInt64, size > Self.hardLimit
        else { return }

        // Drop the oldest 10 % until we are below the limit
        let dropCount = max(Int(Double(records.count) * 0.10), 1)
        let survivors = records.dropLast(dropCount)
        records      = Array(survivors)
        log.warning("Pruned \(dropCount) old records to respect 6 MiB cap.")

        // Rewrite file (streamed)
        do {
            let handle = try FileHandle(forWritingTo: fileURL)
            try handle.truncate(atOffset: 0)

            for rec in survivors.reversed() {                     // oldest → newest
                let line = try Self.encoder.encode(rec)
                try handle.write(contentsOf: line)
                try handle.write(contentsOf: Data("\n".utf8))
            }
            try handle.close()
        } catch {
            log.error("Rewrite after prune failed: \(error.localizedDescription)")
        }
    }

    // MARK: – Debug helper
    public func addMockDataForUITesting() async {
        let now = Date()

        // Create mock data for various apps over the last few hours
        let mockApps = [
            ("com.apple.mobilesafari", "Safari"),
            ("com.apple.mobilemail", "Mail"),
            ("com.apple.MobileSMS", "Messages"),
            ("com.apple.Music", "Music"),
            ("com.apple.camera", "Camera"),
            ("com.apple.mobilenotes", "Notes"),
            ("com.apple.weather", "Weather"),
            ("com.apple.Maps", "Maps")
        ]

        var mockRecords: [ActivityRecord] = []

        for (bundleId, _) in mockApps {
            // Create 3-5 usage sessions for each app
            let sessionCount = Int.random(in: 3...5)

            for _ in 0..<sessionCount {
                let hoursAgo = Double.random(in: 0.5...8.0)
                let sessionDuration = Double.random(in: 30...600) // 30 seconds to 10 minutes

                let endTime = now.addingTimeInterval(-hoursAgo * 3600)
                let startTime = endTime.addingTimeInterval(-sessionDuration)

                let record = ActivityRecord(
                    source: "debug_\(bundleId)",
                    subtype: "app.usage",
                    begin: Int64(startTime.timeIntervalSince1970 * 1000),
                    end: Int64(endTime.timeIntervalSince1970 * 1000),
                    userTimezone: TimeZone.current.secondsFromGMT() / 60, // Convert to minutes
                    workingIntensity: 1.0
                )

                mockRecords.append(record)
            }
        }

        // Sort by time
        mockRecords.sort { $0.begin < $1.begin }

        log.info("Generated \(mockRecords.count) mock activity records")
        await append(contentsOf: mockRecords)
    }

    // Add this method to process data from report extensions
    func processReportData(_ base64String: String) async {
        guard !base64String.isEmpty else {
            log.info("Received empty report data, nothing to process")
            return
        }

        log.info("Processing report data of length: \(base64String.count)")

        guard let jsonData = Data(base64Encoded: base64String) else {
            log.error("Failed to decode base64 string to data")
            return
        }

        do {
            let decodedRecords = try JSONDecoder().decode([ActivityRecord].self, from: jsonData)
            log.info("Successfully decoded \(decodedRecords.count) records from report")

            // Save the records to the file
            await append(contentsOf: decodedRecords)

            // Update the in-memory records on the main thread
            await MainActor.run {
                self.records.append(contentsOf: decodedRecords)
                log.info("Added \(decodedRecords.count) records to in-memory store. Total records now: \(self.records.count)")
            }
        } catch {
            log.error("Failed to decode records from JSON: \(error.localizedDescription)")
        }
    }

    // Add a debug method to check current record count
    func debugRecordCount() async {
        await MainActor.run {
            log.info("Current record count in memory: \(self.records.count)")
        }

        // Check file status and permissions
        let fileManager = FileManager.default
        let filePath = fileURL.path

        log.info("File path: \(filePath)")
        log.info("File exists: \(fileManager.fileExists(atPath: filePath))")
        log.info("File is writable: \(fileManager.isWritableFile(atPath: filePath))")

        // Check file attributes
        do {
            let attributes = try fileManager.attributesOfItem(atPath: filePath)
            if let permissions = attributes[.posixPermissions] as? NSNumber {
                log.info("File permissions: \(String(permissions.uint16Value, radix: 8))")
            }
            if let size = attributes[.size] as? NSNumber {
                log.info("File size: \(size.int64Value) bytes")
            }
        } catch {
            log.error("Failed to get file attributes: \(error.localizedDescription)")
        }

        // Also check file record count
        do {
            let fh = try FileHandle(forReadingFrom: fileURL)
            var lineCount = 0
            for try await _ in fh.bytes.lines {
                lineCount += 1
            }
            try fh.close()
            log.info("Current record count in file: \(lineCount)")
        } catch {
            log.error("Failed to count records in file: \(error.localizedDescription)")
        }

        // Check for fallback file
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let fallbackURL = documentsPath.appendingPathComponent("fallback_activity_records.json")
        if fileManager.fileExists(atPath: fallbackURL.path) {
            log.info("Fallback file exists at: \(fallbackURL.path)")
            do {
                let fallbackData = try Data(contentsOf: fallbackURL)
                let lines = String(data: fallbackData, encoding: .utf8)?.components(separatedBy: .newlines).filter { !$0.isEmpty }.count ?? 0
                log.info("Fallback file contains \(lines) records")
            } catch {
                log.error("Failed to read fallback file: \(error.localizedDescription)")
            }
        }
    }

    // Add a method to manually fix file permissions
    func fixFilePermissions() async {
        log.info("Attempting to fix file permissions...")

        let fileManager = FileManager.default
        let filePath = fileURL.path

        // Remove the file if it exists and recreate it
        if fileManager.fileExists(atPath: filePath) {
            do {
                try fileManager.removeItem(at: fileURL)
                log.info("Removed existing file")
            } catch {
                log.error("Failed to remove existing file: \(error.localizedDescription)")
            }
        }

        // Recreate the file with proper permissions
        let success = fileManager.createFile(atPath: filePath, contents: Data(), attributes: [
            .posixPermissions: 0o666
        ])

        if success {
            log.info("Successfully recreated file with proper permissions")
        } else {
            log.error("Failed to recreate file")
        }

        // Verify the fix worked
        await debugRecordCount()
    }

    // Add method to log real app interactions
    func logInAppInteraction() async {
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios.interaction",
            subtype: "user.interaction",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 1000, // 1 second duration
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )

        await append(contentsOf: [record])
        log.info("Logged in-app interaction")
    }
}
