//
//  ActivityStore.swift — shared between app & extensions
//

import Foundation
import os.log

@MainActor
final class ActivityStore: ObservableObject {

    // MARK: – Single-ton
    static let shared = ActivityStore() 

    @Published private(set) var records: [ActivityRecord] = []

    // MARK: – File locations
    let fileURL: URL
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "ActivityStore")

    // Re-use the same encoders everywhere to avoid per-call allocations.
    static let encoder = JSONEncoder()
    static let decoder = JSONDecoder()

    private init() {
        guard let containerURL = FileManager.default
                .containerURL(forSecurityApplicationGroupIdentifier: PravaIDs.appGroup) else {
            fatalError("App-Group container missing")
        }

        // ◎ Use the container root — simpler permissions than Library/Caches
        self.fileURL = containerURL.appendingPathComponent("activity_records.json")

        // Create the file atomically the very first time
        if !FileManager.default.fileExists(atPath: fileURL.path) {
            let ok = FileManager.default.createFile(atPath: fileURL.path, contents: nil)
            if !ok { log.error("🚫 Could not create ND-JSON file at init()") }
        }

        #if !MONITOR_EXTENSION            // ⟵ monitor must NOT load everything
        // Start a background load that can be cancelled if the process is memory-constrained.
        Task.detached { await self.load() }  // Add explicit 'self' here
        #endif
    }

    // MARK: – Appending
    nonisolated func append(contentsOf newRecords: [ActivityRecord]) async {
        guard !newRecords.isEmpty else { return }
        
        log.info("Attempting to append \(newRecords.count) records to file at \(self.fileURL.path)")
        
        do {
            let fh = try FileHandle(forWritingTo: self.fileURL)
            try fh.seekToEnd()
            
            for rec in newRecords {
                try await fh.write(contentsOf: Self.encoder.encode(rec))
                try fh.write(contentsOf: Data("\n".utf8))
            }
            try fh.close()
            log.info("Successfully appended records to file")
        } catch {
            log.error("File-append failed: \(error.localizedDescription, privacy: .public)")
            return
        }
        
        #if !MONITOR_EXTENSION
        await MainActor.run {
            records.append(contentsOf: newRecords)
            if records.count > 1_000 { records.removeLast(records.count - 1_000) }
        }
        #endif

        await enforceSizeLimit()
    }


    // MARK: – Remove
    public func remove(records submittedRecords: [ActivityRecord]) async {
        let submittedIDs = Set(submittedRecords.map(\.id))
        let survivors    = self.records.filter { !submittedIDs.contains($0.id) }

        guard survivors.count != self.records.count else {
            log.info("No records to remove, store already in sync.")
            return
        }

        records = survivors
        log.info("Removed \(submittedRecords.count) records from in-memory store.")

        // Overwrite with survivors – streamed to avoid a big temporary buffer.
        do {
            let handle = try FileHandle(forWritingTo: fileURL)
            try handle.truncate(atOffset: 0)

            for rec in survivors.reversed() {                       // oldest → newest
                let line = try Self.encoder.encode(rec)
                try handle.write(contentsOf: line)
                try handle.write(contentsOf: Data("\n".utf8))
            }
            try handle.close()
        } catch {
            log.error("Failed to overwrite store: \(error.localizedDescription)")
        }
    }

    // MARK: – Fast aggregate (already streamed)
    nonisolated func reduceTodayTotalSeconds() async throws -> Double {
        let today = Calendar.current.dateInterval(of: .day, for: .now)!
        let fh    = try FileHandle(forReadingFrom: fileURL)
        var acc   = 0.0

        for try await line in fh.bytes.lines {                      // async sequence
            guard let data = line.data(using: .utf8) else { continue }
            guard let rec  = try? await Self.decoder.decode(ActivityRecord.self, from: data) else { continue }
            guard let end  = rec.end else { continue }

            let beginDate = Date(timeIntervalSince1970: Double(rec.begin)/1_000)
            guard today.contains(beginDate) else { continue }

            acc += Double(end - rec.begin)/1_000
        }
        try fh.close()
        return acc
    }

    // MARK: – Clear
    public func clearAll() async {
        do {
            try FileManager.default.removeItem(at: fileURL)
            records = []
            log.info("Cleared all records from the store.")
        } catch let error as NSError where error.code == NSFileNoSuchFileError {
            records = []                                           // nothing to clear
        } catch {
            log.error("Failed to clear store: \(error.localizedDescription)")
        }
    }

    // MARK: – Streamed load (NEW)
    /// Loads *at most* the last `maxLines` records into memory.
    /// Pass `nil` to load everything (only the host app does this).
    public func load(maxLines: Int? = 1_000) async {
        // Keep a small local buffer so the extension never balloons in RAM ≈ WWDC guidance.
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            await MainActor.run { self.records = [] }
            return
        }

        var buffer: [ActivityRecord] = []
        do {
            let fh = try FileHandle(forReadingFrom: fileURL)

            // Iterate **oldest → newest** so we can cap by line-count easily.
            for try await line in fh.bytes.lines {
                autoreleasepool {
                    guard let data = line.data(using: .utf8),
                          let rec  = try? Self.decoder.decode(ActivityRecord.self, from: data)
                    else { return }

                    buffer.append(rec)
                    if let cap = maxLines, buffer.count > cap {
                        buffer.removeFirst(buffer.count - cap)     // drop oldest overflow
                    }
                }
            }
            try fh.close()
        } catch {
            log.error("Stream-load failed: \(error.localizedDescription)")
        }

        await MainActor.run {
            self.records = buffer.sorted { $0.begin > $1.begin }
            log.info("Stream-loaded \(self.records.count) records into memory.")
        }
    }

    // ───────────── File-size guard (≤ 6 MiB) ─────────────
    private static let hardLimit: UInt64 = 6 * 1_048_576     // 6 MiB

    private func enforceSizeLimit() async {
        guard
            let attrs = try? FileManager.default.attributesOfItem(atPath: fileURL.path),
            let size  = attrs[.size] as? UInt64, size > Self.hardLimit
        else { return }

        // Drop the oldest 10 % until we are below the limit
        let dropCount = max(Int(Double(records.count) * 0.10), 1)
        let survivors = records.dropLast(dropCount)
        records      = Array(survivors)
        log.warning("Pruned \(dropCount) old records to respect 6 MiB cap.")

        // Rewrite file (streamed)
        do {
            let handle = try FileHandle(forWritingTo: fileURL)
            try handle.truncate(atOffset: 0)

            for rec in survivors.reversed() {                     // oldest → newest
                let line = try Self.encoder.encode(rec)
                try handle.write(contentsOf: line)
                try handle.write(contentsOf: Data("\n".utf8))
            }
            try handle.close()
        } catch {
            log.error("Rewrite after prune failed: \(error.localizedDescription)")
        }
    }

    // MARK: – Debug helper (unchanged)
    public func addMockDataForUITesting() async { /* … same as before … */ }

    // Add this method to process data from report extensions
    func processReportData(_ base64String: String) async {
        guard !base64String.isEmpty else {
            log.info("Received empty report data, nothing to process")
            return
        }
        
        log.info("Processing report data of length: \(base64String.count)")
        
        guard let jsonData = Data(base64Encoded: base64String) else {
            log.error("Failed to decode base64 string to data")
            return
        }
        
        do {
            let decodedRecords = try JSONDecoder().decode([ActivityRecord].self, from: jsonData)
            log.info("Successfully decoded \(decodedRecords.count) records from report")
            
            // Save the records to the file
            await append(contentsOf: decodedRecords)
            
            // Update the in-memory records on the main thread
            await MainActor.run {
                self.records.append(contentsOf: decodedRecords)
                log.info("Added \(decodedRecords.count) records to in-memory store")
            }
        } catch {
            log.error("Failed to decode records from JSON: \(error.localizedDescription)")
        }
    }
}
