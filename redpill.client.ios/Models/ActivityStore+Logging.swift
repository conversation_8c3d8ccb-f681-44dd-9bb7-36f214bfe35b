//  ActivityStore+Logging.swift
//  ⟡ New file — member of **all three targets**

import Foundation
import os.log

extension ActivityStore {

    /// Appends a one-second slice that represents an explicit user tap inside PravaApp.
    /// Using a finite, ≥1 s duration ensures the record survives later
    /// filtering in `reduceTodayTotalSeconds()` which ignores open-ended slices.:contentReference[oaicite:0]{index=0}
    nonisolated func logInAppInteraction() async {
        let now  = Int64(Date().timeIntervalSince1970 * 1_000)            // ms since epoch
        let tz   = TimeZone.current.secondsFromGMT() / 60                 // Convert to minutes

        let rec  = ActivityRecord(
            source: "debug_pravaapp",
            subtype: "inapp.interaction",
            begin:  now,
            end:    now + 1_000,                                          // 1 s span
            userTimezone: tz,
            workingIntensity: 0.0)

        await append(contentsOf: [rec])                                   // ← already main-safe
    }
}
