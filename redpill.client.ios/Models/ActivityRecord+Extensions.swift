//
//  ActivityRecord+Extensions.swift
//  redpill.client.ios
//
//  Extensions to ActivityRecord to support creation from DeviceActivity data
//

import Foundation
import DeviceActivity

extension ActivityRecord {
    /// Create an ActivityRecord from DeviceActivity app usage data
    static func fromAppUsage(
        appName: String,
        duration: TimeInterval,
        endTime: Date
    ) -> ActivityRecord {
        let startTime = endTime.addingTimeInterval(-duration)
        
        // Convert to milliseconds since epoch for consistency
        let beginMs = Int64(startTime.timeIntervalSince1970 * 1000)
        let endMs = Int64(endTime.timeIntervalSince1970 * 1000)
        
        return ActivityRecord(
            source: appName,
            subtype: "app.usage",
            begin: beginMs,
            end: endMs,
            userTimezone: TimeZone.current.secondsFromGMT(),
            workingIntensity: 1.0
        )
    }
}
