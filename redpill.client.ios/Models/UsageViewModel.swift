//
//  UsageViewModel.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 18/06/2025.
//


//  UsageViewModel.swift
//  ⟡ New file — **App target only**

import Foundation
import Combine

@MainActor                                                                  // guarantees UI-safe writes :contentReference[oaicite:1]{index=1}
final class UsageViewModel: ObservableObject {

    @Published private(set) var todayMinutes: Int = 0
    private var bag = Set<AnyCancellable>()
    private let store = ActivityStore.shared

    init() {
        // Recompute whenever the store changes.
        store.$records
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                Task { await self?.refresh() }
            }
            .store(in: &bag)

        Task { await refresh() }
    }
    
    func refresh() async {
        // Calculate total minutes from today's records
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        let todayRecords = store.records.filter { record in
            let recordDate = Date(timeIntervalSince1970: Double(record.begin) / 1000)
            return calendar.isDate(recordDate, inSameDayAs: today)
        }
        
        // Calculate total duration in minutes
        var totalSeconds = 0.0
        for record in todayRecords {
            if let end = record.end {
                let duration = Double(end - record.begin) / 1000.0 // Convert ms to seconds
                totalSeconds += duration
            }
        }
        
        let minutes = Int(totalSeconds / 60.0)
        
        await MainActor.run {
            self.todayMinutes = minutes
        }
        
        AppLogger.view.debug("Calculated today's usage: \(minutes) minutes from \(todayRecords.count) records")
    }
}
