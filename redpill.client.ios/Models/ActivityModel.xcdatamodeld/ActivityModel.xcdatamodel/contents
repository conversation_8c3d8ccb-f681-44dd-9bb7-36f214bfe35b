<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="ActivityRecordEntity" representedClassName="ActivityRecordEntity" syncable="YES" codeGenerationType="class">
        <attribute name="begin" optional="NO" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="end" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="id" optional="NO" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="source" optional="NO" attributeType="String"/>
        <attribute name="subtype" optional="NO" attributeType="String"/>
        <attribute name="submitted" optional="NO" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="userTimezone" optional="NO" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="workingIntensity" optional="NO" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
    </entity>
</model>
