//  SharedConstants.swift   ◇  add to **all three targets**
//  ----------------------------------------------------------------
//  One place for every identifier the main-app, monitor- and report-
//  extensions must agree on.  By importing this tiny file in every
//  target you eliminate the classic “typo in the App-Group string”
//  that prevents an extension from writing into the container.

import Foundation

enum PravaIDs {
    /// The App-Group that all three bundles share
    static let appGroup = "group.BUH97A4T64.redpill.client.ios"
    
    /// Ensure this matches exactly in Info.plist and code
    static let bgTaskID = "com.redpill.client.ios.submitRecords"
}
