<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>620973311781-rs92ir2p5m7v51splg1n6b36i6rpbjqa.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.620973311781-rs92ir2p5m7v51splg1n6b36i6rpbjqa</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>620973311781-euptgig866bmhgjammee9n7n60icjtcs.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDUwQl2EKWD9QySYeKX5D6FAqeGsDoAySw</string>
	<key>GCM_SENDER_ID</key>
	<string>620973311781</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.redpill.client.ios</string>
	<key>PROJECT_ID</key>
	<string>prava-36a3e</string>
	<key>STORAGE_BUCKET</key>
	<string>prava-36a3e.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:620973311781:ios:e4824983c6e77d8a797ccc</string>
	<key>DATABASE_URL</key>
	<string>https://prava-36a3e-default-rtdb.firebaseio.com</string>
</dict>
</plist>