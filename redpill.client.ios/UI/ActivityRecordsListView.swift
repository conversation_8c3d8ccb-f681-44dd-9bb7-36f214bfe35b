//
//  ActivityRecordsListView.swift
//  redpill.client.ios
//
//  Memory-efficient replacement for DeviceActivityReport
//

import SwiftUI

struct ActivityRecordsListView: View {
    @StateObject private var coreDataStore = CoreDataActivityStore.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Text("Activity Records")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(coreDataStore.records.count) records")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)

            // Records List
            if coreDataStore.records.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "clock.arrow.circlepath")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("No activity records found")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Try generating test data or using other apps to capture activity")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
            } else {
                List {
                    ForEach(coreDataStore.records.prefix(50)) { record in
                        ActivityRecordRowView(record: record)
                    }
                }
                .listStyle(.plain)
            }
        }
        .onAppear {
            Task {
                await coreDataStore.load() // Load from Core Data
            }
        }
    }
}

struct ActivityRecordRowView: View {
    let record: ActivityRecord
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(record.source)
                    .fontWeight(.bold)
                    .foregroundColor(record.source.hasPrefix("debug_") ? .orange : .primary)
                
                Spacer()
                
                Text(record.subtype)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("Duration: \(formatDuration(start: record.begin, end: record.end))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(formatTimestamp(record.begin))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
        .background(Color(.tertiarySystemBackground))
        .cornerRadius(8)
    }
    
    private func formatDuration(start: Int64, end: Int64?) -> String {
        guard let end = end else { return "ongoing" }
        let durationSeconds = Double(end - start) / 1000.0
        let minutes = Int(durationSeconds / 60)
        let seconds = Int(durationSeconds.truncatingRemainder(dividingBy: 60))
        return "\(minutes)m \(seconds)s"
    }
    
    private func formatTimestamp(_ timestamp: Int64) -> String {
        let date = Date(timeIntervalSince1970: Double(timestamp) / 1000.0)
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    ActivityRecordsListView()
}
