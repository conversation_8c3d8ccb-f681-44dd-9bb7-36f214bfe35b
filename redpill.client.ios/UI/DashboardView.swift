//
//  DashboardView.swift
//  redpill.client.ios
//

import SwiftUI

struct DashboardView: View {

    @EnvironmentObject private var focusViewModel: FocusViewModel
    @EnvironmentObject private var activityMonitor: ActivityMonitor
    @EnvironmentObject private var submissionService: SubmissionService

    @StateObject private var activityStore = ActivityStore.shared
    @StateObject private var usageViewModel = UsageViewModel()

    @State private var tapCountForDebug = 0
    @State private var isDebugModeActive = false
    
    // State for the new simulation button's progress view
    @State private var isSimulating: Bool = false

    private static let debugTimestampFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.timeZone = TimeZone.current
        return formatter
    }()

    private func formatTimestamp(_ timestamp: Int64) -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp) / 1000.0)
        return Self.debugTimestampFormatter.string(from: date)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // ── Daily score ring ───────────────────────────────
                CircularProgressView(
                    currentValue: focusViewModel.dailyScore,
                    maxValue:     focusViewModel.dailyScoreMax,
                    lineWidth:    12,
                    showScoreText: true
                )
                .frame(width: 150, height: 150)
                .padding(.top, 20)
                .onTapGesture {
                    tapCountForDebug += 1
                    if tapCountForDebug >= 15 {
                        isDebugModeActive.toggle()
                        AppLogger.view.info("Debug mode toggled: \(self.isDebugModeActive ? "ON" : "OFF")")
                        tapCountForDebug = 0
                    }
                }

                lastSubmissionView

                // ── Main Action Buttons ────────────────────────────────
                mainActionButtons

                // ── Activity Records List ────────────────────────────────
                activityRecordsList

                // ── Debug console (hidden by default) ────────────────────────────────
                if isDebugModeActive {
                    debugConsole
                }
            }
            .padding()
        }
        .onAppear {
            AppLogger.view.info("DashboardView appeared.")
            activityMonitor.currentViewChanged(to: "DashboardView")
        }
        .navigationTitle("Dashboard")
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Main Action Buttons
    @ViewBuilder
    private var mainActionButtons: some View {
        VStack(spacing: 16) {
            // Submit to Backend Button
            Button {
                submissionService.triggerManualSubmission()
            } label: {
                HStack {
                    Image(systemName: "icloud.and.arrow.up")
                        .font(.title2)
                    if submissionService.isSubmitting {
                        Text("Submitting...")
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Text("Submit to Backend")
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
                .font(.headline)
            }
            .disabled(submissionService.isSubmitting)

            // Debug Actions (only when debug mode is active)
            if isDebugModeActive {
                HStack(spacing: 12) {
                    Button("Generate Test Data") {
                        Task { await activityStore.addMockDataForUITesting() }
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.green)

                    Button("Check Auth Status") {
                        let userId = SecureStorage.userId ?? "nil"
                        let userEmail = SecureStorage.userEmail ?? "nil"
                        print("🔐 Auth Status - UserId: \(userId), Email: \(userEmail)")
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .onAppear {
            AppLogger.view.info("DashboardView appeared.")
            activityMonitor.currentViewChanged(to: "DashboardView")
        }
        .navigationTitle("Dashboard")
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Activity Records List
    @ViewBuilder
    private var activityRecordsList: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Text("Local Activity Records")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                let totalRecords = activityStore.records.count
                Text("\(totalRecords) records")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // Records List
            if totalRecords > 0 {
                List {
                    ForEach(activityStore.records.prefix(15)) { record in
                        recordRowView(record: record, source: "Local")
                    }
                }
                .frame(maxHeight: 300)
                .listStyle(.plain)
            } else {
                Text("No activity records found")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    // MARK: - Debug Console
    @ViewBuilder
    private var debugConsole: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Debug Console")
                .font(.headline)
                .fontWeight(.semibold)

            Text("Today's Usage: \(usageViewModel.todayMinutes) minutes")
                .font(.subheadline)
                .foregroundColor(.secondary)

            HStack(spacing: 8) {
                Button("Debug Store") {
                    Task {
                        await activityStore.debugRecordCount()
                    }
                }
                .buttonStyle(.bordered)

                Button("Refresh Monitoring") {
                    Task {
                        await activityMonitor.refreshMonitoring()
                        try? await Task.sleep(nanoseconds: 2_000_000_000)
                        await activityStore.load()
                    }
                }
                .buttonStyle(.bordered)
            }
        }
        .padding()
        .background(Color(.tertiarySystemBackground))
        .cornerRadius(12)
    }

    // MARK: - Helper Views and Methods

    /// A view that displays the last successful submission timestamp.
    @ViewBuilder
    private var lastSubmissionView: some View {
        Group {
            if let date = submissionService.lastSuccessfulSubmission {
                Text("Last sync: \(date.formatted(date: .abbreviated, time: .shortened))")
            } else {
                Text("Last sync: Never")
            }
        }
        .font(.caption)
        .foregroundColor(.secondary)
        .padding(.bottom, 10)
    }

    private func formatDuration(start: Int64, end: Int64?) -> String {
        guard let end = end else { return "ongoing" }
        let durationSeconds = Double(end - start) / 1000.0
        let minutes = Int(durationSeconds / 60)
        let seconds = Int(durationSeconds.truncatingRemainder(dividingBy: 60))
        return "\(minutes)m \(seconds)s"
    }

    @MainActor
    private func formatTimestamp(_ timestamp: Int64) -> String {
        let date = Date(timeIntervalSince1970: Double(timestamp) / 1000.0)
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    @ViewBuilder
    private func recordRowView(record: ActivityRecord, source: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(record.source)
                    .fontWeight(.bold)
                    .foregroundColor(record.source.hasPrefix("debug_") ? .orange : .primary)
                Spacer()
                Text(source)
                    .font(.caption)
                    .foregroundColor(source == "Core Data" ? .green : .blue)
            }

            HStack {
                Text("Duration: \(formatDuration(start: record.begin, end: record.end))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text(formatTimestamp(record.begin))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
        .background(Color(.tertiarySystemBackground))
        .cornerRadius(8)
    }
}
