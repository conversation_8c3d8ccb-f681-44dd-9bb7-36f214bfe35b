//
//  DashboardView.swift
//  redpill.client.ios
//

import SwiftUI

struct DashboardView: View {

    @EnvironmentObject private var focusViewModel: FocusViewModel
    @EnvironmentObject private var activityMonitor: ActivityMonitor
    @EnvironmentObject private var submissionService: SubmissionService

    @StateObject private var activityStore = ActivityStore.shared
    @StateObject private var usageViewModel = UsageViewModel()

    @State private var tapCountForDebug = 0
    @State private var isDebugModeActive = false
    
    // State for the new simulation button's progress view
    @State private var isSimulating: Bool = false

    private static let debugTimestampFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.timeZone = TimeZone.current
        return formatter
    }()

    private func formatTimestamp(_ timestamp: Int64) -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp) / 1000.0)
        return Self.debugTimestampFormatter.string(from: date)
    }

    var body: some View {
        VStack(spacing: 15) {
            // ── Daily score ring ───────────────────────────────
            CircularProgressView(
                currentValue: focusViewModel.dailyScore,
                maxValue:     focusViewModel.dailyScoreMax,
                lineWidth:    12,
                showScoreText: true
            )
            .frame(width: 150, height: 150)
            .padding(.top, 20)
            .onTapGesture {
                tapCountForDebug += 1
                if tapCountForDebug >= 15 {
                    isDebugModeActive.toggle()
                    AppLogger.view.info("Debug mode toggled: \(self.isDebugModeActive ? "ON" : "OFF")")
                    tapCountForDebug = 0
                }
            }
            
            lastSubmissionView
            
            // ── Debug console ────────────────────────────────
            if isDebugModeActive {
                VStack {
                    Text("Today's Usage: \(usageViewModel.todayMinutes) minutes")
                        .font(.headline)
                        .padding(.vertical, 8)
                    
                    List {
                        ForEach(activityStore.records.prefix(10)) { record in
                            VStack(alignment: .leading) {
                                Text(record.source).bold()
                                Text("Duration: \(formatDuration(start: record.begin, end: record.end))")
                                    .font(.caption)
                            }
                        }
                    }
                    .frame(height: 200)
                    .listStyle(PlainListStyle())
                    
                    HStack {
                        Button("Generate Test Data") {
                            Task { await activityStore.addMockDataForUITesting() }
                        }
                        .buttonStyle(.bordered)
                        
                        Button("Log Interaction") {
                            Task { await activityStore.logInAppInteraction() }
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding(.vertical, 8)
                }
                .padding()
                .background(Color(.secondarySystemBackground))
                .cornerRadius(12)
                .padding(.horizontal)
                
                HStack {
                    Text("Today: \(UsageViewModel().todayMinutes)m")
                        .font(.headline)
                    Spacer()
                    Button("Log in-app Interaction") {
                        Task { await ActivityStore.shared.logInAppInteraction() }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding(.horizontal)
                
                VStack {
                    Button("Generate Mock Records") {
                        Task {
                            await activityStore.addMockDataForUITesting()
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.green)
                    
                    Button {
                        submissionService.triggerManualSubmission()
                    } label: {
                        if submissionService.isSubmitting {
                            ProgressView("Submitting…")
                                .frame(maxWidth: .infinity)
                        } else {
                            Text("Force Foreground Submit")
                                .frame(maxWidth: .infinity)
                        }
                    }
                    .buttonStyle(.bordered)
                    .disabled(submissionService.isSubmitting)

                    Button {
                        Task {
                            self.isSimulating = true
                            if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                                let success = await submissionService.performSubmission()
                                AppLogger.view.info("Simulated background submission completed with success: \(success)")
                            }
                            self.isSimulating = false
                        }
                    } label: {
                        if isSimulating {
                            ProgressView("Simulating Background Sync...")
                                .frame(maxWidth: .infinity)
                        } else {
                            Text("Simulate Background Sync")
                                .frame(maxWidth: .infinity)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isSimulating)
                }
                .padding(.horizontal)
            } else {
                Spacer()
            }

            // ── Example in-app navigation log ────────────────
            Button("Log In-App Interaction") {
                Task {
                    await ActivityStore.shared.logInAppInteraction()
                    activityMonitor.currentViewChanged(to: "DashboardButtonTap")
                }
            }
            .padding()
            .buttonStyle(.bordered)
        }
        .onAppear {
            AppLogger.view.info("DashboardView appeared.")
            activityMonitor.currentViewChanged(to: "DashboardView")
        }
        .navigationTitle("Dashboard")
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Subviews

    /// A view that displays the last successful submission timestamp.
    @ViewBuilder
    private var lastSubmissionView: some View {
        Group {
            if let date = submissionService.lastSuccessfulSubmission {
                Text("Last sync: \(date.formatted(date: .abbreviated, time: .shortened))")
            } else {
                Text("Last sync: Never")
            }
        }
        .font(.caption)
        .foregroundColor(.secondary)
        .padding(.bottom, 10)
    }

    /// The list view for showing unsynced records.
    @ViewBuilder
    private var debugActivityListView: some View {
        VStack {
            Text("Unsynced Records (\(activityStore.records.count))")
                .font(.headline)
                .padding(.top)
            
            List(activityStore.records.prefix(500), id: \.id) { record in
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(record.source).fontWeight(.bold)
                        Spacer()
                        Text(record.subtype).foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Begin:").fontWeight(.semibold)
                        Text(formatTimestamp(record.begin))
                    }

                    HStack {
                        Text("End:").fontWeight(.semibold)
                        if let end = record.end {
                            Text(formatTimestamp(end))
                        } else {
                            Text("Running…").italic().foregroundColor(.orange)
                        }
                    }
                    
                    if let end = record.end {
                        let duration = TimeInterval((end - record.begin) / 1000)
                        Text("Duration: \(duration, format: .number.precision(.fractionLength(2))) s")
                            .fontWeight(.semibold)
                    }
                }
                .font(.system(size: 10, design: .monospaced))
                .padding(.vertical, 4)
            }
            .listStyle(.plain)
            .frame(minHeight: 100, maxHeight: 300) // Give the list a defined size
            .onAppear {
                AppLogger.view.debug("Debug list appeared with \(self.activityStore.records.count) records.")
            }
        }
    }

    private func formatDuration(start: Int64, end: Int64?) -> String {
        guard let end = end else { return "ongoing" }
        let durationSeconds = Double(end - start) / 1000.0
        let minutes = Int(durationSeconds / 60)
        let seconds = Int(durationSeconds.truncatingRemainder(dividingBy: 60))
        return "\(minutes)m \(seconds)s"
    }
}
