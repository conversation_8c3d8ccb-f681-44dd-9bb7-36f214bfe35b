//
//  ActivityReportView.swift
//  redpill.client.ios
//

import SwiftUI
import DeviceActivity
import FamilyControls

struct ActivityReportView: View {

    let context: DeviceActivityReport.Context
    let baseFilter: DeviceActivityFilter

    @State private var selection = FamilyActivitySelection()
    @State private var isPickerPresented = false
    @State private var refreshKey = UUID()
    @EnvironmentObject private var activityMonitor: ActivityMonitor
    @StateObject private var activityStore = CoreDataActivityStore.shared
    @EnvironmentObject private var submissionService: SubmissionService
    @EnvironmentObject private var authViewModel: AuthViewModel

    var body: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                HStack {
                    Button("Select apps / categories") {
                        isPickerPresented = true
                    }
                    .font(.headline)
                    .familyActivityPicker(isPresented: $isPickerPresented,
                                          selection: $selection)

                    Spacer()

                    Button("Refresh") {
                        Task {
                            await activityMonitor.refreshMonitoring()
                            refreshKey = UUID()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }

                HStack {
                    Button("Generate Test Data") {
                        Task {
                            await activityStore.addMockDataForUITesting()
                            refreshKey = UUID()
                        }
                    }
                    .buttonStyle(.bordered)

                    Button("Log Interaction") {
                        Task {
                            await activityStore.logInAppInteraction()
                            refreshKey = UUID()
                        }
                    }
                    .buttonStyle(.bordered)

                    Spacer()
                }

                HStack {
                    VStack(alignment: .leading) {
                        Text("Records: \(activityStore.records.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("Auth: \(authViewModel.isSignedIn ? "✅" : "❌")")
                            .font(.caption2)
                            .foregroundColor(authViewModel.isSignedIn ? .green : .red)

                        if let lastSubmission = submissionService.lastSuccessfulSubmission {
                            Text("Last submit: \(lastSubmission, style: .time)")
                                .font(.caption2)
                                .foregroundColor(.green)
                        } else {
                            Text("Never submitted")
                                .font(.caption2)
                                .foregroundColor(.orange)
                        }
                    }

                    Spacer()

                    Button("Debug Count") {
                        Task {
                            await activityStore.debugRecordCount()
                        }
                    }
                    .buttonStyle(.bordered)
                    .font(.caption)

                    Button("Fix File") {
                        Task {
                            await activityStore.fixFilePermissions()
                        }
                    }
                    .buttonStyle(.bordered)
                    .font(.caption)

                    Button("Submit to Backend") {
                        submissionService.triggerManualSubmission()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(submissionService.isSubmitting)

                    if submissionService.isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .padding(.horizontal)

            DeviceActivityReport(
                context,
                filter: refinedFilter(from: baseFilter, using: selection)
            )
            .id(refreshKey) // Force refresh when key changes
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .navigationTitle("Activity Report")
        .onAppear {
            // Refresh data when view appears
            Task {
                await activityMonitor.refreshMonitoring()
                refreshKey = UUID()
            }
        }
        .onChange(of: selection) {
            // Refresh when selection changes
            refreshKey = UUID()
        }
    }

    private func refinedFilter(from filter: DeviceActivityFilter,
                               using selection: FamilyActivitySelection) -> DeviceActivityFilter {

        guard !selection.applicationTokens.isEmpty ||
              !selection.categoryTokens.isEmpty ||
              !selection.webDomainTokens.isEmpty else { return filter }

        return DeviceActivityFilter(
            applications: selection.applicationTokens,
            categories:   selection.categoryTokens,
            webDomains:   selection.webDomainTokens
        )
    }
}
