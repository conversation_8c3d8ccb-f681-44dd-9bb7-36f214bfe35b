//
//  ActivityReportView.swift
//  redpill.client.ios
//

import SwiftUI
import DeviceActivity
import FamilyControls

struct ActivityReportView: View {

    let context: DeviceActivityReport.Context
    let baseFilter: DeviceActivityFilter

    @State private var selection = FamilyActivitySelection()
    @State private var isPickerPresented = false

    var body: some View {
        VStack {
            Button("Select apps / categories") { isPickerPresented = true }
                .font(.headline)
                .padding(.horizontal)
                .familyActivityPicker(isPresented: $isPickerPresented,
                                      selection: $selection)

            DeviceActivityReport(
                context,
                filter: refinedFilter(from: baseFilter, using: selection)
            )
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .navigationTitle("Activity Report")
        // --- FIX: Updated `onChange` for iOS 17+ ---
        .onChange(of: selection) {
            // The view re-evaluates automatically, no action needed.
        }
    }

    private func refinedFilter(from filter: DeviceActivityFilter,
                               using selection: FamilyActivitySelection) -> DeviceActivityFilter {

        guard !selection.applicationTokens.isEmpty ||
              !selection.categoryTokens.isEmpty ||
              !selection.webDomainTokens.isEmpty else { return filter }

        return DeviceActivityFilter(
            applications: selection.applicationTokens,
            categories:   selection.categoryTokens,
            webDomains:   selection.webDomainTokens
        )
    }
}
