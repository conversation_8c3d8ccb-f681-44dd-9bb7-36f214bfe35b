//
//  CircularProgressView.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//


import SwiftUI

struct CircularProgressView: View {
    let currentValue: Double
    let maxValue: Double
    let lineWidth: CGFloat
    let showScoreText: Bool
    
    private var normalizedProgress: Double {
        max(0.0, min(1.0, currentValue / maxValue))
    }
    
    private var barColor: Color {
        let percentage = normalizedProgress
        if percentage < 0.33 {
            return Color(red: 255/255, green: 89/255, blue: 94/255)
        } else if percentage < 0.66 {
            return Color(red: 255/255, green: 202/255, blue: 58/255)
        } else {
            return Color(red: 4/255, green: 129/255, blue: 120/255)
        }
    }
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.2), lineWidth: lineWidth)
            Circle()
                .trim(from: 0, to: CGFloat(normalizedProgress))
                .stroke(barColor, style: StrokeStyle(lineWidth: lineWidth, lineCap: .round))
                .rotationEffect(.degrees(-90))
            if showScoreText {
                VStack {
                    Text("\(Int(currentValue))")
                        .font(.largeTitle)
                        .foregroundColor(.white)
                    Text("REAL-TIME SCORE")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.65))
                }
            }
        }
    }
}
