//
//  TotalActivityView.swift
//  Shared between App & ActivityReportExtension
//

import SwiftUI

/// Concrete SwiftUI view that shows the total foreground time.
/// Declared `Sendable` to satisfy “Complete” strict-concurrency checking.
struct TotalActivityView: View, Sendable {
    let totalActivity: String

    var body: some View {
        Text(totalActivity)
            .font(.title)
            .bold()
    }
}
