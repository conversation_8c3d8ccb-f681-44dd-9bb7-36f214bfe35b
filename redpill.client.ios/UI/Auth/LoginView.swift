//
//  LoginView.swift
//  redpill.client.ios
//
//  Created by <PERSON> on 25/05/2025.
//


import SwiftUI

struct LoginView: View {
    @EnvironmentObject private var authViewModel: AuthViewModel
    @State private var email: String = ""
    @State private var password: String = ""
    
    var body: some View {
        VStack {
            TextField("Email", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            SecureField("Password", text: $password)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            But<PERSON>("Sign In") {
                Task { await authViewModel.signIn(email: email, password: password) }
            }
            <PERSON><PERSON>("Sign In with Google") {
                Task {
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootVC = windowScene.windows.first?.rootViewController {
                        await authViewModel.signInWithGoogle(presentingViewController: rootVC)
                    }
                }
            }
            if let error = authViewModel.errorMessage {
                Text(error).foregroundColor(.red)
            }
        }
        .padding()
    }
}