//
//  ActivityMonitor.swift
//  redpill.client.ios
//
//  Centralised wrapper around DeviceActivityCenter that tracks high-level
//  app events (view navigation, foreground/background) and schedules
//  system-level usage monitoring.
//
//  Member of **all three targets** so both the extensions and the host
//  app can post view-change breadcrumbs.
//

@preconcurrency import DeviceActivity         // silences non-Sendable warnings while Apple audits API
import Foundation
import FamilyControls
import os.log
import SwiftUI                                 // needed for @Published

@MainActor
final class ActivityMonitor: ObservableObject {

    // MARK: – Public, observable state
    /// Last screen or interaction recorded by the host app.
    @Published var currentView: String = "launch"

    // MARK: – Private
    private let log    = Logger(subsystem: "com.redpill.client.ios", category: "ActivityMonitor")
    private let center = DeviceActivityCenter()

    /// Stable identifiers required by DeviceActivity APIs
    private static let activityName = DeviceActivityName("global.usage.monitoring")
    private static let eventName    = DeviceActivityEvent.Name("global.usage.monitoring.event")

    //  ActivityMonitor.swift  – replace the whole startMonitoring() body
    func startMonitoring() async {
        log.info("Attempting to start monitoring…")

        let schedule = DeviceActivitySchedule(
            intervalStart: DateComponents(hour: 0, minute: 0),
            intervalEnd:   DateComponents(hour: 23, minute: 59),
            repeats:       true,
            warningTime:   nil)

        // Create a more sensitive threshold to capture more data
        let event = DeviceActivityEvent(
            applications: [],  // Empty means "all applications"
            categories:   [],  // Empty means "all categories"
            webDomains:   [],  // Empty means "all web domains"
            threshold:    DateComponents(second: 30))  // Reduced threshold to 30 seconds

        do {
            try center.startMonitoring(
                Self.activityName,
                during: schedule,
                events: [Self.eventName : event])
            log.info("✅ Device-activity monitoring started.")
        } catch {
            log.error("🔴 Failed to start monitoring: \(error.localizedDescription)")
        }
    }



    func stopMonitoring() {
        log.info("Stopping monitoring.")
        center.stopMonitoring([Self.activityName])
    }

    // MARK: – High-level app breadcrumbs
    /// Call from host-app views to note navigation or UI events.
    func currentViewChanged(to viewIdentifier: String) {
        currentView = viewIdentifier
        log.debug("View changed ➜ \(viewIdentifier, privacy: .public)")
    }

    // Optional helpers exposed to the `redpill_client_iosApp`
    func appDidBecomeActive()  { log.info("App became active.") }
    func appDidEnterBackground() { log.info("App entered background.") }
}
