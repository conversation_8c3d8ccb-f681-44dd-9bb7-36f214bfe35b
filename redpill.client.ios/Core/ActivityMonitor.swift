//
//  ActivityMonitor.swift
//  redpill.client.ios
//
//  Centralised wrapper around Device<PERSON>ctivityCenter that tracks high-level
//  app events (view navigation, foreground/background) and schedules
//  system-level usage monitoring.
//
//  Member of **all three targets** so both the extensions and the host
//  app can post view-change breadcrumbs.
//

@preconcurrency import DeviceActivity         // silences non-Sendable warnings while Apple audits API
import Foundation
import FamilyControls
import os.log
import SwiftUI                                 // needed for @Published
import Darwin.Mach                             // for memory monitoring

@MainActor
final class ActivityMonitor: ObservableObject {

    // MARK: – Public, observable state
    /// Last screen or interaction recorded by the host app.
    @Published var currentView: String = "launch"

    // MARK: – Private
    private let log    = Logger(subsystem: "com.redpill.client.ios", category: "ActivityMonitor")
    private let center = DeviceActivityCenter()

    /// Stable identifiers required by DeviceActivity APIs
    private static let activityName = DeviceActivityName("global.usage.monitoring")
    private static let eventName    = DeviceActivityEvent.Name("global.usage.monitoring.event")

    //  ActivityMonitor.swift  – replace the whole startMonitoring() body
    func startMonitoring() async {
        log.info("Attempting to start monitoring…")

        let schedule = DeviceActivitySchedule(
            intervalStart: DateComponents(hour: 0, minute: 0),
            intervalEnd:   DateComponents(hour: 23, minute: 59),
            repeats:       true,
            warningTime:   nil)

        // Create a more sensitive threshold to capture real user activity
        let event = DeviceActivityEvent(
            applications: [],  // Empty means "all applications"
            categories:   [],  // Empty means "all categories"
            webDomains:   [],  // Empty means "all web domains"
            threshold:    DateComponents(minute: 1))  // 1-minute threshold for better real-time tracking

        do {
            try center.startMonitoring(
                Self.activityName,
                during: schedule,
                events: [Self.eventName : event])
            log.info("✅ Device-activity monitoring started.")
        } catch {
            log.error("🔴 Failed to start monitoring: \(error.localizedDescription)")
        }
    }



    func stopMonitoring() {
        log.info("Stopping monitoring.")
        center.stopMonitoring([Self.activityName])
    }

    /// Force refresh monitoring to capture latest data with memory safety
    func refreshMonitoring() async {
        log.info("Refreshing monitoring to capture latest data...")

        // Check memory pressure before proceeding
        if await isMemoryPressureHigh() {
            log.warning("High memory pressure detected, skipping refresh to prevent crash")
            return
        }

        // Stop current monitoring
        center.stopMonitoring([Self.activityName])

        // Wait a moment
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Restart monitoring
        await startMonitoring()

        // Also trigger a manual data collection as fallback
        await collectCurrentActivityData()
    }

    /// Check if memory pressure is high to prevent crashes
    private func isMemoryPressureHigh() async -> Bool {
        // Get current memory usage
        let info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let memoryUsage = info.resident_size
            // Conservative limit: 12MB for extensions, 50MB for main app
            let memoryLimit: UInt64 = 12 * 1024 * 1024 // 12MB

            if memoryUsage > memoryLimit {
                log.warning("Memory usage (\(memoryUsage / 1024 / 1024)MB) exceeds safe limit (\(memoryLimit / 1024 / 1024)MB)")
                return true
            }
        }

        return false
    }

    /// Manual data collection as fallback when DeviceActivity isn't working
    private func collectCurrentActivityData() async {
        log.info("Collecting current activity data manually...")

        // Create a simple activity record for the current app usage
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios",
            subtype: "app.session",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: nil, // Ongoing session
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )

        await ActivityStore.shared.append(contentsOf: [record])
        log.info("Added manual activity record")
    }

    // MARK: – High-level app breadcrumbs
    /// Call from host-app views to note navigation or UI events.
    func currentViewChanged(to viewIdentifier: String) {
        currentView = viewIdentifier
        log.debug("View changed ➜ \(viewIdentifier, privacy: .public)")
    }

    // Optional helpers exposed to the `redpill_client_iosApp`
    func appDidBecomeActive()  { log.info("App became active.") }
    func appDidEnterBackground() { log.info("App entered background.") }
}
