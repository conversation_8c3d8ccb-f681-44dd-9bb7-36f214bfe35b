//
//  ActivityEventCollector.swift
//  redpill.client.ios
//
//  Simple event-based activity collection for timeline reconstruction
//

import Foundation
import UIKit
import os.log

@MainActor
final class ActivityEventCollector: ObservableObject {
    static let shared = ActivityEventCollector()
    
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "ActivityEventCollector")
    private let coreDataStore = CoreDataActivityStore.shared
    
    private init() {
        setupAppStateMonitoring()
    }
    
    // MARK: - App State Monitoring
    
    private func setupAppStateMonitoring() {
        // Monitor app lifecycle events
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { await self?.logAppEvent("app.became_active") }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { await self?.logAppEvent("app.entered_background") }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.willTerminateNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { await self?.logAppEvent("app.will_terminate") }
        }
    }
    
    // MARK: - Event Logging
    
    func logAppEvent(_ eventType: String) async {
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios",
            subtype: eventType,
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 100, // 100ms event duration
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )
        
        await coreDataStore.append(contentsOf: [record])
        log.info("Logged app event: \(eventType)")
    }
    
    func logUserInteraction(_ interactionType: String, in view: String) async {
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios.\(view)",
            subtype: "user.\(interactionType)",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 500, // 500ms interaction duration
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )
        
        await coreDataStore.append(contentsOf: [record])
        log.info("Logged user interaction: \(interactionType) in \(view)")
    }
    
    func logViewChange(from: String, to: String) async {
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios.navigation",
            subtype: "view.change",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 100, // 100ms navigation event
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )
        
        await coreDataStore.append(contentsOf: [record])
        log.info("Logged view change: \(from) → \(to)")
    }
    
    // MARK: - Manual Event Generation for Testing
    
    func generateTestEvents() async {
        let now = Date()
        var events: [ActivityRecord] = []
        
        // Generate a sequence of realistic events over the last hour
        let eventTypes = [
            ("app.launch", "app.lifecycle"),
            ("view.dashboard", "navigation"),
            ("user.tap", "interaction"),
            ("view.activity", "navigation"),
            ("user.scroll", "interaction"),
            ("app.background", "app.lifecycle"),
            ("app.foreground", "app.lifecycle"),
            ("user.refresh", "interaction")
        ]
        
        for (index, (eventType, category)) in eventTypes.enumerated() {
            let eventTime = now.addingTimeInterval(-Double(index * 300)) // 5 minutes apart
            
            let record = ActivityRecord(
                source: "debug_event_\(category)",
                subtype: eventType,
                begin: Int64(eventTime.timeIntervalSince1970 * 1000),
                end: Int64(eventTime.timeIntervalSince1970 * 1000) + 1000, // 1 second duration
                userTimezone: TimeZone.current.secondsFromGMT() / 60,
                workingIntensity: 1.0
            )
            
            events.append(record)
        }
        
        await coreDataStore.append(contentsOf: events)
        log.info("Generated \(events.count) test activity events")
    }
}
